<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // This migration is now handled by the main Contact to Tenant migration
        // Skip this migration as it's been superseded by the main migration
        // which properly handles all the constraint updates and column renames
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is now handled by the main Contact to Tenant migration
        // Skip this migration as it's been superseded
    }
};
