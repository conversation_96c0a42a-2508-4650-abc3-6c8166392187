<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix tenant_imports table - rename existing_contact_id to existing_tenant_id
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign('contact_imports_existing_contact_id_foreign');
            $table->renameColumn('existing_contact_id', 'existing_tenant_id');
            $table->foreign('existing_tenant_id')->references('id')->on('tenants')->onDelete('set null');
        });

        // Fix tenant_groups table - rename contact_id to tenant_id
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign('contact_groups_contact_id_foreign');
            $table->dropUnique('contact_groups_contact_id_group_id_unique');
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->unique(['tenant_id', 'group_id']);
        });

        // Fix message_recipients table - rename contact_id to tenant_id
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign('message_recipients_contact_id_foreign');
            $table->dropUnique('message_recipients_message_contact_channel_unique');
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->unique(['message_id', 'tenant_id', 'channel']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse message_recipients changes
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->dropUnique(['message_id', 'tenant_id', 'channel']);
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->unique(['message_id', 'contact_id', 'channel']);
        });

        // Reverse tenant_groups changes
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->dropUnique(['tenant_id', 'group_id']);
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->unique(['contact_id', 'group_id']);
        });

        // Reverse tenant_imports changes
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_tenant_id']);
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            $table->foreign('existing_contact_id')->references('id')->on('tenants')->onDelete('set null');
        });
    }
};
