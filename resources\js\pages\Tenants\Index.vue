<template>
  <div>

    <Head title="Tenants" />
    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading
              title="Tenants Management"
              description="Manage all tenants in the system and their communication preferences"
            />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
                {{ props.tenants.total }} {{ props.tenants.total === 1 ? 'tenant' : 'tenants' }}
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-green-500 rounded-full"></span>
                {{ props.statusCounts.active }} active
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-gray-400 rounded-full"></span>
                {{ props.statusCounts.inactive }} inactive
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button variant="outline" size="lg" class="shadow-sm hover:shadow-md transition-all duration-200" asChild>
              <Link href="/tenant-import">
                <Upload class="mr-2 h-5 w-5" />
                Import Tenants
              </Link>
            </Button>
            <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
              <Link href="/tenants/create">
                <PlusIcon class="mr-2 h-5 w-5" />
                Add Tenant
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert />

        <!-- Search and Filters -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
            <!-- Search Input -->
            <div class="relative w-full max-w-lg">
              <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search contacts by name, email, or phone..."
                class="pl-10 h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary min-w-[320px]"
                :model-value="search"
                @update:model-value="val => search = String(val)"
              />
            </div>

            <!-- Status Filter Dropdown -->
            <DropdownMenu v-model:open="isDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Status Filter
                  <Badge v-if="hasSelectedStatus" variant="secondary" class="ml-2 font-normal">
                    {{ selectedStatusCount }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[220px]">
                <DropdownMenuLabel class="flex items-center justify-between">
                  <span>Filter by status</span>
                  <transition name="fade" mode="out-in">
                    <span v-if="showLoading" class="text-xs text-muted-foreground">(Loading...)</span>
                  </transition>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2">
                  <div v-for="status in statuses" :key="status.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
                    <Checkbox
                      :id="status.value"
                      :modelValue="isStatusChecked(status.value)"
                      @update:modelValue="(checked) => onCheckboxChange(checked, status.value)"
                      :disabled="isLoading"
                      class="peer"
                      @click.stop
                    />
                    <label
                      :for="status.value"
                      class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                      :class="{ 'opacity-50': isLoading }"
                      @click.prevent="onCheckboxChange(!isStatusChecked(status.value), status.value)"
                    >
                      <span class="flex items-center gap-2">
                        <span class="h-2 w-2 rounded-full" :class="status.value === 'active' ? 'bg-green-500' : 'bg-gray-400'"></span>
                        {{ status.label }}
                      </span>
                      <Badge variant="secondary" class="text-xs">{{ status.count }}</Badge>
                    </label>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <!-- Building Filter Dropdown -->
            <DropdownMenu v-model:open="isBuildingDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Building Filter
                  <Badge v-if="selectedBuildings.length > 0" variant="secondary" class="ml-2 font-normal">
                    {{ selectedBuildings.length }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[300px]">
                <DropdownMenuLabel class="flex items-center justify-between">
                  <span>Filter by building</span>
                  <transition name="fade" mode="out-in">
                    <span v-if="showLoading" class="text-xs text-muted-foreground">(Loading...)</span>
                  </transition>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2 space-y-2">
                  <div class="relative">
                    <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input 
                      type="search" 
                      placeholder="Search buildings..." 
                      class="pl-8" 
                      v-model="buildingSearch"
                    />
                  </div>
                  <div class="max-h-[200px] overflow-y-auto space-y-1">
                    <div v-for="property in filteredProperties" :key="property.id" class="relative flex items-center space-x-2 py-1">
                      <Checkbox 
                        :id="'property-' + property.id" 
                        :modelValue="isBuildingChecked(property.id)"
                        @update:modelValue="(checked) => onBuildingCheckboxChange(checked, property.id)" 
                        :disabled="isLoading"
                        class="peer" 
                        @click.stop 
                      />
                      <label
                        :for="'property-' + property.id"
                        class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                        :class="{ 'opacity-50': isLoading }"
                        @click.prevent="onBuildingCheckboxChange(!isBuildingChecked(property.id), property.id)"
                      >
                        <span class="flex items-center gap-2">
                          <Building class="h-3 w-3 text-muted-foreground" />
                          {{ property.name }}
                        </span>
                        <Badge variant="secondary" class="text-xs">{{ property.contactCount || 0 }}</Badge>
                      </label>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <!-- Group Filter Dropdown -->
            <DropdownMenu v-model:open="isGroupDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Group Filter
                  <Badge v-if="selectedGroups.length > 0" variant="secondary" class="ml-2 font-normal">
                    {{ selectedGroups.length }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[300px]">
                <DropdownMenuLabel class="flex items-center justify-between">
                  <span>Filter by group</span>
                  <transition name="fade" mode="out-in">
                    <span v-if="showLoading" class="text-xs text-muted-foreground">(Loading...)</span>
                  </transition>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2 space-y-2">
                  <div class="relative">
                    <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search groups..."
                      class="pl-8"
                      v-model="groupSearch"
                    />
                  </div>
                  <div class="max-h-[200px] overflow-y-auto space-y-1">
                    <!-- No Group Option -->
                    <div class="relative flex items-center space-x-2 py-1">
                      <Checkbox
                        id="group-none"
                        :modelValue="isGroupChecked('none')"
                        @update:modelValue="(checked) => onGroupCheckboxChange(checked, 'none')"
                        :disabled="isLoading"
                        class="peer"
                        @click.stop
                      />
                      <label
                        for="group-none"
                        class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                        :class="{ 'opacity-50': isLoading }"
                        @click.prevent="onGroupCheckboxChange(!isGroupChecked('none'), 'none')"
                      >
                        <span class="flex items-center gap-2">
                          <div class="w-4 h-4 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <span class="text-xs text-gray-600 dark:text-gray-300">∅</span>
                          </div>
                          <span class="text-gray-600 dark:text-gray-300">No Group</span>
                        </span>
                        <Badge
                          variant="secondary"
                          class="text-xs"
                          :style="{
                            '--tw-bg-opacity': '1',
                            'background-color': '#e5e7eb',
                            'color': '#374151'
                          }"
                        >
                          {{ props.noGroupCount || 0 }}
                        </Badge>
                      </label>
                    </div>

                    <!-- Regular Groups -->
                    <div v-for="group in filteredGroups" :key="group.id" class="relative flex items-center space-x-2 py-1">
                      <Checkbox
                        :id="'group-' + group.id"
                        :modelValue="isGroupChecked(group.id)"
                        @update:modelValue="(checked) => onGroupCheckboxChange(checked, group.id)"
                        :disabled="isLoading"
                        class="peer"
                        @click.stop
                      />
                      <label
                        :for="'group-' + group.id"
                        class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                        :class="{ 'opacity-50': isLoading }"
                        @click.prevent="onGroupCheckboxChange(!isGroupChecked(group.id), group.id)"
                      >
                        <span class="flex items-center gap-2">
                          <div
                            class="w-4 h-4 rounded-full text-white flex items-center justify-center"
                            :style="{
                              backgroundColor: (group.color || '#6366f1') + ' !important',
                              backgroundImage: 'none !important'
                            }"
                          >
                            <Users class="h-2.5 w-2.5" />
                          </div>
                          {{ group.name }}
                        </span>
                        <Badge
                          variant="secondary"
                          class="text-xs text-white"
                          :style="{
                            backgroundColor: (group.color || '#6366f1') + ' !important',
                            backgroundImage: 'none !important'
                          }"
                        >
                          {{ group.contactCount || 0 }}
                        </Badge>
                      </label>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

          <!-- Action Buttons -->
            <IconButton
              variant="outline"
              @click="updateFilters()"
              :disabled="isLoading"
              tooltip="Refresh data"
              class="h-11 w-11 shadow-sm"
            >
              <RotateCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
            </IconButton>
            <Button
              v-if="hasActiveFilters"
              variant="ghost"
              @click="resetFilters"
              class="h-11 text-muted-foreground hover:text-foreground"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        <!-- Contacts Table -->
        <DataTable
          v-model:sort="sort"
          :pagination="{
            currentPage: props.contacts.current_page,
            lastPage: props.contacts.last_page,
            perPage,
            total: props.contacts.total,
            itemLabel: 'contact'
          }"
          :is-loading="isLoading"
          loading-text="Loading contacts..."
          @page-change="goToPage"
          @update:per-page="perPage = $event"
        >
          <template #default="{ sort, onSort }">
            <div class="table-container">
              <UITable class="border-0">
                <TableHeader>
                  <TableRow class="table-header-row">
                    <DataTableHead :sort="sort" field="first_name" @sort="onSort" class="table-header-cell">
                      Contact
                    </DataTableHead>
                    <DataTableHead :sort="sort" field="email" @sort="onSort" class="table-header-cell">
                      Email & Phone
                    </DataTableHead>
                    <TableHead class="table-header-cell">Contact Methods</TableHead>
                    <DataTableHead :sort="sort" field="property_id" @sort="onSort" class="table-header-cell">
                      Property & Unit
                    </DataTableHead>
                    <DataTableHead :sort="sort" field="status" @sort="onSort" class="table-header-cell">
                      Status
                    </DataTableHead>
                    <DataTableHead :sort="sort" field="created_at" @sort="onSort" class="table-header-cell">
                      Created
                    </DataTableHead>
                    <TableHead class="table-header-cell">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <!-- Empty State Row -->
                  <TableRow v-if="!props.contacts.data.length" class="table-row">
                    <TableCell :colspan="7" class="h-32 text-center">
                      <div class="flex flex-col items-center justify-center space-y-4 py-8">
                        <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <Users class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                        </div>
                        <div class="space-y-2">
                          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {{ hasActiveFilters ? 'No matching contacts found' : 'No contacts found' }}
                          </h3>
                          <p class="text-sm text-muted-foreground max-w-sm">
                            {{ hasActiveFilters ? 'Try adjusting your search or filters to find what you\'re looking for.' : 'Get started by adding your first contact to the system.' }}
                          </p>
                        </div>
                        <div class="flex items-center gap-2">
                          <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="resetFilters">
                            Clear Filters
                          </Button>
                          <Button size="sm" as-child>
                            <Link href="/contacts/create">
                              <PlusIcon class="mr-1 h-3 w-3" />
                              Add Contact
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>

                  <!-- Data Rows -->
                  <TableRow
                    v-for="contact in props.contacts.data"
                    :key="contact.id"
                    class="table-row"
                  >
                    <!-- Contact Info -->
                    <TableCell class="table-cell-primary">
                      <div class="flex items-center gap-3">
                        <div class="avatar-primary">
                          <Users class="h-4 w-4" />
                        </div>
                        <div class="flex flex-col">
                          <Link
                            :href="`/contacts/${contact.id}`"
                            class="font-medium text-gray-900 dark:text-gray-100 hover:text-primary transition-colors cursor-pointer"
                          >
                            {{ contact.first_name }} {{ contact.last_name }}
                          </Link>
                          <span class="text-xs text-muted-foreground">
                            ID: #{{ contact.id }}
                          </span>
                        </div>
                      </div>
                    </TableCell>

                    <!-- Email & Phone -->
                    <TableCell class="table-cell">
                      <div class="flex flex-col gap-1">
                        <div v-if="contact.email" class="flex items-center gap-2">
                          <Mail class="h-3 w-3 text-muted-foreground" />
                          <span class="text-sm">{{ contact.email }}</span>
                        </div>
                        <div v-if="contact.mobile_phone" class="flex items-center gap-2">
                          <Phone class="h-3 w-3 text-muted-foreground" />
                          <span class="text-sm">{{ contact.mobile_phone }}</span>
                        </div>
                        <div v-if="!contact.email && !contact.mobile_phone" class="text-xs text-muted-foreground">
                          No contact info
                        </div>
                      </div>
                    </TableCell>
                    <!-- Contact Methods -->
                    <TableCell class="table-cell">
                      <div class="flex items-center gap-2">
                        <div v-if="contact.contact_sms"
                          class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30"
                          title="SMS Contact"
                        >
                          <MessageSquare class="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div v-if="contact.contact_wa"
                          class="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/30"
                          title="WhatsApp Contact"
                        >
                          <MessageCircle class="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <div v-if="contact.contact_email"
                          class="flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-amber-900/30"
                          title="Email Contact"
                        >
                          <Mail class="h-3 w-3 text-amber-600 dark:text-amber-400" />
                        </div>
                        <div v-if="!contact.contact_sms && !contact.contact_wa && !contact.contact_email"
                          class="text-xs text-muted-foreground"
                        >
                          No methods
                        </div>
                      </div>
                    </TableCell>

                    <!-- Property & Unit -->
                    <TableCell class="table-cell">
                      <div class="flex flex-col gap-1">
                        <div v-if="contact.property" class="flex items-center gap-2">
                          <Building class="h-3 w-3 text-muted-foreground" />
                          <span class="text-sm font-medium">{{ contact.property.name }}</span>
                        </div>
                        <div v-if="contact.unit_number" class="flex items-center gap-2 ml-5">
                          <span class="text-xs text-muted-foreground">Unit: {{ contact.unit_number }}</span>
                        </div>
                        <div v-if="!contact.property" class="text-xs text-muted-foreground">
                          No property assigned
                        </div>
                      </div>
                    </TableCell>

                    <!-- Status -->
                    <TableCell class="table-cell">
                      <div class="flex items-center gap-2">
                        <div v-if="contact.status" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                          Active
                        </div>
                        <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                          <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                          Inactive
                        </div>
                      </div>
                    </TableCell>
                    <!-- Created Date -->
                    <TableCell class="table-cell">
                        <DateTime :date="contact.created_at" />
                    </TableCell>

                    <!-- Actions -->
                    <TableCell class="table-cell">
                      <div class="flex items-center gap-2">
                        <ActionButton variant="outline" tooltip="View contact" as-child>
                          <Link :href="`/contacts/${contact.id}`">
                            <Eye class="h-4 w-4" />
                          </Link>
                        </ActionButton>
                        <ActionButton variant="outline" tooltip="Edit contact" as-child>
                          <Link :href="`/contacts/${contact.id}/edit`">
                            <Pencil class="h-4 w-4" />
                          </Link>
                        </ActionButton>
                        <ActionButton
                          @click="confirmDelete(contact)"
                          variant="destructive"
                          tooltip="Delete contact"
                        >
                          <Trash2 class="h-4 w-4" />
                        </ActionButton>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </UITable>
            </div>
          </template>
        </DataTable>

        <!-- Delete Confirmation Dialog -->
        <AlertDialog v-model:open="showDeleteDialog">
          <AlertDialogContent>
            <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this contact? This action cannot be undone.
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel @click="showDeleteDialog = false">Cancel</AlertDialogCancel>
              <AlertDialogAction @click="performDelete">Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { ChevronRight, PlusIcon, Search, Pencil, Trash2, RotateCw, MessageSquare, MessageCircle, Mail, Upload, Users, Phone, Building, Eye } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import DateTime from '@/components/ui/DateTime.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import ActionButton from '@/components/ui/ActionButton.vue';
import IconButton from '@/components/ui/IconButton.vue';
import { PageSize } from '@/types/pagination';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import debounce from 'lodash/debounce';
import type { PaginatedContacts, ContactFilters, SortOptions, Contact } from '@/types/contact';

interface Props {
  tenants: PaginatedContacts;
  sort: SortOptions;
  filters: ContactFilters;
  statusCounts: {
    active: number;
    inactive: number;
  };
  properties: {
    id: number;
    name: string;
    tenantCount: number;
  }[];
  groups: {
    id: number;
    name: string;
    tenantCount: number;
    color?: string;
  }[];
  noGroupCount?: number;
}

const props = defineProps<Props>();

// State
const sort = ref<SortOptions>(props.sort || { field: 'id', direction: 'asc' });
const search = ref(props.filters?.search || '');
const selectedStatus = ref<('active' | 'inactive')[]>(
  Array.isArray(props.filters?.status)
    ? props.filters.status.filter((status): status is 'active' | 'inactive' => status === 'active' || status === 'inactive')
    : []
);
const perPage = ref<PageSize>(props.filters?.per_page || PageSize.Small);
const tenantToDelete = ref<Contact | null>(null);
const showDeleteDialog = ref(false);
const isDropdownOpen = ref(false);
const isBuildingDropdownOpen = ref(false);
const isGroupDropdownOpen = ref(false);
const isLoading = ref(false);
const buildingSearch = ref('');
const groupSearch = ref('');
const selectedBuildings = ref<number[]>(Array.isArray(props.filters?.buildings) ? props.filters.buildings : []);
const selectedGroups = ref<(number | string)[]>(Array.isArray(props.filters?.groups) ? props.filters.groups : []);

// Constants
const breadcrumbs = [{ title: 'Tenants', href: '/tenants' }];

interface StatusOption {
  label: string;
  value: 'active' | 'inactive';
  count: number;
}

const statuses = computed<StatusOption[]>(() => [
  { label: 'Active', value: 'active', count: props.statusCounts.active },
  { label: 'Inactive', value: 'inactive', count: props.statusCounts.inactive },
]);

// Computed
const hasSelectedStatus = computed(() => selectedStatus.value.length > 0);

const selectedStatusCount = computed(() => {
  return selectedStatus.value.reduce((total, status) => {
    if (status === 'active') {
      return total + (props.statusCounts?.active || 0);
    } else if (status === 'inactive') {
      return total + (props.statusCounts?.inactive || 0);
    }
    return total;
  }, 0);
});

const showLoading = computed(() => isLoading.value);

const hasActiveFilters = computed(() => {
  return Boolean(props.filters.search) ||
         (props.filters.status && props.filters.status.length > 0) ||
         (props.filters.buildings && props.filters.buildings.length > 0) ||
         (props.filters.groups && props.filters.groups.length > 0);
});

const filteredProperties = computed(() => {
  if (!buildingSearch.value) {
    return props.properties;
  }
  const searchTerm = buildingSearch.value.toLowerCase();
  return props.properties.filter(property =>
    property.name.toLowerCase().includes(searchTerm)
  );
});

const filteredGroups = computed(() => {
  if (!groupSearch.value) {
    return props.groups;
  }
  const searchTerm = groupSearch.value.toLowerCase();
  return props.groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm)
  );
});

// Methods
const isStatusChecked = (value: 'active' | 'inactive'): boolean => {
  return selectedStatus.value.includes(value);
};

const onCheckboxChange = (checked: unknown, value: 'active' | 'inactive') => {
  const currentStatus = [...selectedStatus.value];
  const newStatus = checked === true
    ? [...currentStatus, value]
    : currentStatus.filter(status => status !== value);

  selectedStatus.value = newStatus;
  debouncedUpdateFilters();
};

const isBuildingChecked = (buildingId: number): boolean => {
  return selectedBuildings.value.includes(buildingId);
};

const onBuildingCheckboxChange = (checked: unknown, buildingId: number) => {
  const currentBuildings = [...selectedBuildings.value];
  const newBuildings = checked === true
    ? [...currentBuildings, buildingId]
    : currentBuildings.filter(id => id !== buildingId);

  selectedBuildings.value = newBuildings;
  debouncedUpdateFilters();
};

const isGroupChecked = (groupId: number | string): boolean => {
  if (groupId === 'none') {
    return selectedGroups.value.includes('none' as any);
  }
  return selectedGroups.value.includes(groupId as number);
};

const onGroupCheckboxChange = (checked: unknown, groupId: number | string) => {
  const currentGroups = [...selectedGroups.value];
  const newGroups = checked === true
    ? [...currentGroups, groupId as any]
    : currentGroups.filter(id => id !== groupId);

  selectedGroups.value = newGroups;
  debouncedUpdateFilters();
};

const updateFilters = (params = {}) => {
  isLoading.value = true;
  router.visit(
    route('tenants.index', {
      search: search.value,
      status: selectedStatus.value,
      buildings: selectedBuildings.value,
      groups: selectedGroups.value,
      sort: sort.value,
      per_page: perPage.value,
      ...params
    }),
    {
      preserveState: true,
      preserveScroll: true,
      replace: true,
      only: ['tenants', 'filters'],
      onFinish: () => {
        isLoading.value = false;
      },
      onError: () => {
        isLoading.value = false;
      }
    }
  );
};

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const goToPage = (page: number) => {
  updateFilters({ page });
};

const resetFilters = () => {
  search.value = '';
  selectedStatus.value = [];
  selectedBuildings.value = [];
  selectedGroups.value = [];
  buildingSearch.value = '';
  groupSearch.value = '';
  updateFilters();
};

const confirmDelete = (tenant: Contact) => {
  tenantToDelete.value = tenant;
  showDeleteDialog.value = true;
};

const performDelete = () => {
  if (tenantToDelete.value) {
    router.delete(`/tenants/${tenantToDelete.value.id}`, {
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => {
        showDeleteDialog.value = false;
        tenantToDelete.value = null;
      },
    });
  }
};

// Watchers
watch(search, () => debouncedUpdateFilters());
watch(sort, () => updateFilters(), { deep: true }); // Add deep watcher for sort object
watch(perPage, () => {
  updateFilters({ page: 1 }); // Reset to page 1 when changing per page
});
watch(buildingSearch, () => {
  // We don't need to trigger an API call when building search changes
  // since it's just filtering the local properties list
});

</script>