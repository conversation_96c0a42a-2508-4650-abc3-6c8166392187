<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Property extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the tenants for the property.
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Get the number of tenants associated with the property.
     *
     * @return int
     */
    public function getTenantsCountAttribute(): int
    {
        return $this->tenants()->count();
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use tenants() instead
     */
    public function contacts(): Has<PERSON>any
    {
        return $this->tenants();
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use getTenantsCountAttribute() instead
     */
    public function getContactsCountAttribute(): int
    {
        return $this->getTenantsCountAttribute();
    }
}