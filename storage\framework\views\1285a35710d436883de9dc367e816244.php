<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token() ?? 'no-csrf'); ?>">
    <title>Database Seeders - <?php echo e(config('app.name')); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading { display: none; }
        .loading.show { display: inline-block; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Database Setup</h1>
            <p class="text-gray-600 mb-6">Run database migrations and seeders to set up your application.</p>
            
            <!-- Warning Alert -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Important</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>These operations will modify your database. Use with caution in production environments.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Complete Setup -->
            <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                <h2 class="text-lg font-semibold text-green-900 mb-2">Complete Setup</h2>
                <p class="text-green-700 mb-3">Run migrations and create the default user account in one step:</p>
                <button onclick="runCompleteSetup()" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out mr-3">
                    <span class="loading animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                    Complete Setup
                </button>
                <button onclick="checkMigrationStatus()" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                    <span class="loading animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                    Check Status
                </button>
            </div>

            <!-- Individual Operations -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <!-- Migrations -->
                <div class="bg-purple-50 border border-purple-200 rounded-md p-4">
                    <h2 class="text-lg font-semibold text-purple-900 mb-2">Database Migrations</h2>
                    <p class="text-purple-700 mb-3">Create and update database tables:</p>
                    <button onclick="runMigrations()" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                        <span class="loading animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                        Run Migrations
                    </button>
                </div>

                <!-- Default User -->
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h2 class="text-lg font-semibold text-blue-900 mb-2">Default User</h2>
                    <p class="text-blue-700 mb-3">Create the default user account for initial login:</p>
                    <button onclick="runDefaultUserSeeder()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                        <span class="loading animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                        Create Default User
                    </button>
                </div>
            </div>

            <!-- Available Seeders -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-900">Available Seeders</h2>
                
                <?php $__currentLoopData = $seeders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $seeder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-gray-200 rounded-md p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900"><?php echo e($seeder['name']); ?></h3>
                            <p class="text-gray-600"><?php echo e($seeder['description']); ?></p>
                            <code class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded"><?php echo e($seeder['class']); ?></code>
                        </div>
                        <button onclick="runSeeder('<?php echo e($key); ?>')" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                            <span class="loading animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                            Run Seeder
                        </button>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Results Area -->
            <div id="results" class="mt-6 hidden">
                <h2 class="text-xl font-semibold text-gray-900 mb-3">Results</h2>
                <div id="results-content" class="bg-gray-50 border border-gray-200 rounded-md p-4">
                    <!-- Results will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get CSRF token (may not be available if middleware is bypassed)
        const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
        const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : null;

        function getHeaders(includeContentType = true) {
            const headers = {};

            if (includeContentType) {
                headers['Content-Type'] = 'application/json';
            }

            // Only add CSRF token if available
            if (csrfToken && csrfToken !== 'no-csrf') {
                headers['X-CSRF-TOKEN'] = csrfToken;
            }

            return headers;
        }

        function showLoading(button) {
            const loading = button.querySelector('.loading');
            loading.classList.add('show');
            button.disabled = true;
        }

        function hideLoading(button) {
            const loading = button.querySelector('.loading');
            loading.classList.remove('show');
            button.disabled = false;
        }

        function showResults(data, success = true) {
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            let html = '';
            
            if (success) {
                html += `<div class="flex items-center mb-3">
                    <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-green-800 font-medium">${data.message || 'Success'}</span>
                </div>`;
            } else {
                html += `<div class="flex items-center mb-3">
                    <svg class="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-red-800 font-medium">${data.error || 'Error occurred'}</span>
                </div>`;
            }
            
            if (data.output) {
                html += `<div class="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm whitespace-pre-wrap">${data.output}</div>`;
            }
            
            if (data.credentials) {
                html += `<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <h4 class="font-medium text-yellow-800">Login Credentials:</h4>
                    <p class="text-yellow-700">Email: <code>${data.credentials.email}</code></p>
                    <p class="text-yellow-700">Password: <code>${data.credentials.password}</code></p>
                    <p class="text-yellow-600 text-sm mt-1">⚠ Please change the password after first login!</p>
                </div>`;
            }
            
            resultsContent.innerHTML = html;
            resultsDiv.classList.remove('hidden');
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }

        async function runDefaultUserSeeder() {
            const button = event.target;
            showLoading(button);

            try {
                const response = await fetch('/seeders/default-user', {
                    method: 'POST',
                    headers: getHeaders(),
                });

                const data = await response.json();
                showResults(data, data.success);

            } catch (error) {
                showResults({ error: 'Network error occurred' }, false);
            } finally {
                hideLoading(button);
            }
        }

        async function runSeeder(seederKey) {
            const button = event.target;
            showLoading(button);

            try {
                const response = await fetch('/seeders/run', {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({ seeder: seederKey }),
                });

                const data = await response.json();
                showResults(data, data.success);

            } catch (error) {
                showResults({ error: 'Network error occurred' }, false);
            } finally {
                hideLoading(button);
            }
        }

        async function runMigrations() {
            const button = event.target;
            showLoading(button);

            try {
                const response = await fetch('/seeders/migrate', {
                    method: 'POST',
                    headers: getHeaders(),
                });

                const data = await response.json();
                showResults(data, data.success);

            } catch (error) {
                showResults({ error: 'Network error occurred' }, false);
            } finally {
                hideLoading(button);
            }
        }

        async function checkMigrationStatus() {
            const button = event.target;
            showLoading(button);

            try {
                const response = await fetch('/seeders/migrate/status', {
                    method: 'GET',
                    headers: getHeaders(),
                });

                const data = await response.json();
                showResults(data, data.success);

            } catch (error) {
                showResults({ error: 'Network error occurred' }, false);
            } finally {
                hideLoading(button);
            }
        }

        async function runCompleteSetup() {
            const button = event.target;
            showLoading(button);

            try {
                const response = await fetch('/seeders/complete-setup', {
                    method: 'POST',
                    headers: getHeaders(),
                });

                const data = await response.json();

                // Show detailed results for complete setup
                let html = '';

                if (data.success) {
                    html += `<div class="flex items-center mb-3">
                        <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-green-800 font-medium">${data.message}</span>
                    </div>`;
                } else {
                    html += `<div class="flex items-center mb-3">
                        <svg class="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-red-800 font-medium">${data.message}</span>
                    </div>`;
                }

                if (data.results) {
                    // Migration results
                    if (data.results.migration) {
                        html += `<div class="mb-4">
                            <h4 class="font-medium text-gray-900 mb-2">Migration Results:</h4>
                            <div class="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm whitespace-pre-wrap">${data.results.migration.output}</div>
                        </div>`;
                    }

                    // User creation results
                    if (data.results.user && data.results.user.credentials) {
                        html += `<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                            <h4 class="font-medium text-yellow-800">Login Credentials:</h4>
                            <p class="text-yellow-700">Email: <code>${data.results.user.credentials.email}</code></p>
                            <p class="text-yellow-700">Password: <code>${data.results.user.credentials.password}</code></p>
                            <p class="text-yellow-600 text-sm mt-1">⚠ Please change the password after first login!</p>
                        </div>`;
                    }
                }

                const resultsDiv = document.getElementById('results');
                const resultsContent = document.getElementById('results-content');
                resultsContent.innerHTML = html;
                resultsDiv.classList.remove('hidden');
                resultsDiv.scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                showResults({ error: 'Network error occurred' }, false);
            } finally {
                hideLoading(button);
            }
        }
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\contact-center\resources\views/seeders/index.blade.php ENDPATH**/ ?>