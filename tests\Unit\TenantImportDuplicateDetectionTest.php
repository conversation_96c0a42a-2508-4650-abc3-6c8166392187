<?php

namespace Tests\Unit;

use App\Services\TenantImportService;
use Tests\TestCase;

class TenantImportDuplicateDetectionTest extends TestCase
{
    protected TenantImportService $importService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->importService = new TenantImportService();
    }



    /**
     * Test duplicate detection within imported data
     */
    public function test_duplicate_detection_within_imported_data()
    {
        $processedRows = [
            [
                'data' => [
                    'unit_number' => '1005',
                    'email' => '<EMAIL>',
                    'first_name' => 'Nasrin',
                    'last_name' => 'Fathima',
                ],
                'errors' => [],
                'has_errors' => false,
                'raw_data' => []
            ],
            [
                'data' => [
                    'unit_number' => '1005',
                    'email' => '<EMAIL>', // Same unit and email as row 0
                    'first_name' => 'Nasrin',
                    'last_name' => 'Fathima',
                ],
                'errors' => [],
                'has_errors' => false,
                'raw_data' => []
            ],
            [
                'data' => [
                    'unit_number' => '1005',
                    'email' => '<EMAIL>', // Same unit but different email
                    'first_name' => 'Different',
                    'last_name' => 'Person',
                ],
                'errors' => [],
                'has_errors' => false,
                'raw_data' => []
            ]
        ];

        // Test row 1 (index 1) - should find duplicate at index 0
        $duplicateIndex = $this->importService->findDuplicateInImportedData(
            $processedRows[1]['data'], 
            $processedRows, 
            1
        );
        $this->assertEquals(0, $duplicateIndex);

        // Test row 2 (index 2) - should NOT find duplicate (different email)
        $duplicateIndex2 = $this->importService->findDuplicateInImportedData(
            $processedRows[2]['data'], 
            $processedRows, 
            2
        );
        $this->assertNull($duplicateIndex2);
    }


}
