<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename contacts table to tenants
        Schema::rename('contacts', 'tenants');
        
        // Rename contact_imports table to tenant_imports
        Schema::rename('contact_imports', 'tenant_imports');
        
        // Rename contact_groups table to tenant_groups
        Schema::rename('contact_groups', 'tenant_groups');
        
        // Update foreign key references in tenant_imports table
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_contact_id']);
            $table->renameColumn('existing_contact_id', 'existing_tenant_id');
            $table->foreign('existing_tenant_id')->references('id')->on('tenants')->onDelete('set null');
        });
        
        // Update foreign key references in tenant_groups table
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
        
        // Update foreign key references in message_recipients table
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
        
        // Update unique constraint in tenant_groups table
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropUnique(['contact_id', 'group_id']);
            $table->unique(['tenant_id', 'group_id']);
        });
        
        // Update unique constraint in message_recipients table if it exists
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop existing unique constraint that includes contact_id
            $table->dropUnique(['message_id', 'contact_id', 'channel']);
            // Add new unique constraint with tenant_id
            $table->unique(['message_id', 'tenant_id', 'channel']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the unique constraint changes in message_recipients
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropUnique(['message_id', 'tenant_id', 'channel']);
            $table->unique(['message_id', 'contact_id', 'channel']);
        });
        
        // Reverse the unique constraint changes in tenant_groups
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropUnique(['tenant_id', 'group_id']);
            $table->unique(['contact_id', 'group_id']);
        });
        
        // Reverse foreign key changes in message_recipients
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });
        
        // Reverse foreign key changes in tenant_groups
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });
        
        // Reverse foreign key changes in tenant_imports
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_tenant_id']);
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            $table->foreign('existing_contact_id')->references('id')->on('contacts')->onDelete('set null');
        });
        
        // Rename tables back to original names
        Schema::rename('tenant_groups', 'contact_groups');
        Schema::rename('tenant_imports', 'contact_imports');
        Schema::rename('tenants', 'contacts');
    }
};
