<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only handle what's actually needed based on current database state

        // Step 1: Handle message_recipients table (the only one that still needs contact_id -> tenant_id)
        if (Schema::hasColumn('message_recipients', 'contact_id') && !Schema::hasColumn('message_recipients', 'tenant_id')) {
            // Drop constraints first - handle database driver differences
            $driver = DB::connection()->getDriverName();

            Schema::table('message_recipients', function (Blueprint $table) use ($driver) {
                // Only try to drop constraints in MySQL, skip in SQLite for tests
                if ($driver === 'mysql') {
                    try {
                        $table->dropUnique('message_recipients_message_contact_channel_unique');
                    } catch (\Exception) {
                        // Constraint might not exist, continue
                    }
                }

                // Drop foreign key
                try {
                    $table->dropForeign(['contact_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
            });

            // Rename the column
            Schema::table('message_recipients', function (Blueprint $table) {
                $table->renameColumn('contact_id', 'tenant_id');
            });
        }

        // Step 2: Rename main table if needed
        if (Schema::hasTable('contacts') && !Schema::hasTable('tenants')) {
            Schema::rename('contacts', 'tenants');
        }

        // Step 3: Add constraints back
        if (Schema::hasColumn('message_recipients', 'tenant_id')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                try {
                    $table->unique(['message_id', 'tenant_id', 'channel']);
                } catch (\Exception) {
                    // Constraint might already exist
                }

                try {
                    $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
                } catch (\Exception) {
                    // Foreign key might already exist
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints first - only if tables exist
        if (Schema::hasTable('tenant_imports')) {
            Schema::table('tenant_imports', function (Blueprint $table) {
                try {
                    $table->dropForeign(['existing_tenant_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
            });
        }

        if (Schema::hasTable('tenant_groups')) {
            Schema::table('tenant_groups', function (Blueprint $table) {
                try {
                    $table->dropForeign(['tenant_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
            });
        }

        if (Schema::hasTable('message_recipients')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                try {
                    $table->dropForeign(['tenant_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
            });
        }

        // Rename tables back to original names - only if they exist
        if (Schema::hasTable('tenant_groups') && !Schema::hasTable('contact_groups')) {
            Schema::rename('tenant_groups', 'contact_groups');
        }

        if (Schema::hasTable('tenant_imports') && !Schema::hasTable('contact_imports')) {
            Schema::rename('tenant_imports', 'contact_imports');
        }

        if (Schema::hasTable('tenants') && !Schema::hasTable('contacts')) {
            Schema::rename('tenants', 'contacts');
        }

        // First, drop the current unique constraints
        if (Schema::hasTable('message_recipients')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                try {
                    $table->dropUnique(['message_id', 'tenant_id', 'channel']);
                } catch (\Exception) {
                    // Constraint might not exist, continue
                }
            });
        }

        if (Schema::hasTable('contact_groups')) {
            Schema::table('contact_groups', function (Blueprint $table) {
                try {
                    $driver = DB::connection()->getDriverName();
                    if ($driver === 'mysql') {
                        $indexExists = DB::select("SHOW INDEX FROM `contact_groups` WHERE Key_name = 'contact_groups_tenant_id_group_id_unique'");
                        if (!empty($indexExists)) {
                            $table->dropUnique(['tenant_id', 'group_id']);
                        }
                    }
                } catch (\Exception) {
                    // Constraint might not exist, continue
                }
            });
        }

        // Then, reverse column renames - only if columns exist
        if (Schema::hasTable('message_recipients') && Schema::hasColumn('message_recipients', 'tenant_id')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                $table->renameColumn('tenant_id', 'contact_id');
            });
        }

        if (Schema::hasTable('contact_groups') && Schema::hasColumn('contact_groups', 'tenant_id')) {
            Schema::table('contact_groups', function (Blueprint $table) {
                $table->renameColumn('tenant_id', 'contact_id');
            });
        }

        if (Schema::hasTable('contact_imports') && Schema::hasColumn('contact_imports', 'existing_tenant_id')) {
            Schema::table('contact_imports', function (Blueprint $table) {
                $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            });
        }

        // Finally, add back the old constraints and foreign keys
        if (Schema::hasTable('message_recipients') && Schema::hasColumn('message_recipients', 'contact_id')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                try {
                    $table->unique(['message_id', 'contact_id', 'channel']);
                } catch (\Exception) {
                    // Constraint might already exist, continue
                }

                try {
                    $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
                } catch (\Exception) {
                    // Foreign key might already exist, continue
                }
            });
        }

        if (Schema::hasTable('contact_groups') && Schema::hasColumn('contact_groups', 'contact_id')) {
            Schema::table('contact_groups', function (Blueprint $table) {
                try {
                    $table->unique(['contact_id', 'group_id']);
                } catch (\Exception) {
                    // Constraint might already exist, continue
                }

                try {
                    $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
                } catch (\Exception) {
                    // Foreign key might already exist, continue
                }
            });
        }

        if (Schema::hasTable('contact_imports') && Schema::hasColumn('contact_imports', 'existing_contact_id')) {
            Schema::table('contact_imports', function (Blueprint $table) {
                try {
                    $driver = DB::connection()->getDriverName();
                    if ($driver === 'mysql') {
                        $foreignKeyExists = DB::select("SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'contact_imports' AND COLUMN_NAME = 'existing_contact_id' AND REFERENCED_TABLE_NAME = 'contacts'");
                        if (empty($foreignKeyExists)) {
                            $table->foreign('existing_contact_id')->references('id')->on('contacts')->onDelete('set null');
                        }
                    }
                } catch (\Exception $e) {
                    // Log or handle the exception if needed
                }
            });
        }
    }
};
