<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update foreign key references in contact_imports table before renaming
        Schema::table('contact_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_contact_id']);
            $table->renameColumn('existing_contact_id', 'existing_tenant_id');
        });

        // Update foreign key references in contact_groups table before renaming
        Schema::table('contact_groups', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
            $table->renameColumn('contact_id', 'tenant_id');
        });

        // Update unique constraints before renaming columns
        Schema::table('contact_groups', function (Blueprint $table) {
            try {
                $table->dropUnique(['contact_id', 'group_id']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
        });

        // Only drop constraints if not using SQLite (to avoid test issues)
        $driver = Schema::getConnection()->getDriverName();
        if ($driver !== 'sqlite') {
            Schema::table('message_recipients', function (Blueprint $table) {
                // Drop existing unique constraint that includes contact_id
                // Try all possible constraint formats and names
                try {
                    $table->dropUnique(['message_id', 'contact_id', 'channel']);
                } catch (\Exception) {
                    // Try the original constraint format without channel
                    try {
                        $table->dropUnique(['message_id', 'contact_id']);
                    } catch (\Exception) {
                        // Try explicit constraint names
                        try {
                            $table->dropUnique('message_recipients_message_id_contact_id_unique');
                        } catch (\Exception) {
                            try {
                                $table->dropUnique('message_recipients_message_contact_channel_unique');
                            } catch (\Exception) {
                                // No constraint exists, continue
                            }
                        }
                    }
                }
            });
        }

        // Update foreign key references and rename columns
        Schema::table('contact_imports', function (Blueprint $table) {
            if (Schema::hasColumn('contact_imports', 'existing_contact_id')) {
                try {
                    $table->dropForeign(['existing_contact_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
                $table->renameColumn('existing_contact_id', 'existing_tenant_id');
            }
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            if (Schema::hasColumn('contact_groups', 'contact_id')) {
                try {
                    $table->dropForeign(['contact_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
                $table->renameColumn('contact_id', 'tenant_id');
            }
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            if (Schema::hasColumn('message_recipients', 'contact_id')) {
                try {
                    $table->dropForeign(['contact_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
                $table->renameColumn('contact_id', 'tenant_id');
            }
        });

        // Add new unique constraints with renamed columns
        Schema::table('contact_groups', function (Blueprint $table) {
            $table->unique(['tenant_id', 'group_id']);
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->unique(['message_id', 'tenant_id', 'channel']);
        });

        // Now rename the tables
        Schema::rename('contacts', 'tenants');
        Schema::rename('contact_imports', 'tenant_imports');
        Schema::rename('contact_groups', 'tenant_groups');

        // Add back foreign key constraints with new table names
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->foreign('existing_tenant_id')->references('id')->on('tenants')->onDelete('set null');
        });

        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints first
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_tenant_id']);
        });

        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        // Rename tables back to original names
        Schema::rename('tenant_groups', 'contact_groups');
        Schema::rename('tenant_imports', 'contact_imports');
        Schema::rename('tenants', 'contacts');

        // Reverse the unique constraint changes
        Schema::table('message_recipients', function (Blueprint $table) {
            try {
                $table->dropUnique(['message_id', 'tenant_id', 'channel']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            $table->unique(['message_id', 'contact_id', 'channel']);
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            try {
                $table->dropUnique(['tenant_id', 'group_id']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            $table->unique(['contact_id', 'group_id']);
        });

        // Reverse column renames and add back foreign keys
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_imports', function (Blueprint $table) {
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            $table->foreign('existing_contact_id')->references('id')->on('contacts')->onDelete('set null');
        });
    }
};
