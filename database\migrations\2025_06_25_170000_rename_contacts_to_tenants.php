<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only handle what's actually needed based on current database state

        // Step 1: Handle message_recipients table (the only one that still needs contact_id -> tenant_id)
        if (Schema::hasColumn('message_recipients', 'contact_id') && !Schema::hasColumn('message_recipients', 'tenant_id')) {
            // Drop constraints first - handle database driver differences
            $driver = DB::connection()->getDriverName();

            Schema::table('message_recipients', function (Blueprint $table) use ($driver) {
                // Only try to drop constraints in MySQL, skip in SQLite for tests
                if ($driver === 'mysql') {
                    try {
                        $table->dropUnique('message_recipients_message_contact_channel_unique');
                    } catch (\Exception) {
                        // Constraint might not exist, continue
                    }
                }

                // Drop foreign key
                try {
                    $table->dropForeign(['contact_id']);
                } catch (\Exception) {
                    // Foreign key might not exist, continue
                }
            });

            // Rename the column
            Schema::table('message_recipients', function (Blueprint $table) {
                $table->renameColumn('contact_id', 'tenant_id');
            });
        }

        // Step 2: Rename main table if needed
        if (Schema::hasTable('contacts') && !Schema::hasTable('tenants')) {
            Schema::rename('contacts', 'tenants');
        }

        // Step 3: Add constraints back
        if (Schema::hasColumn('message_recipients', 'tenant_id')) {
            Schema::table('message_recipients', function (Blueprint $table) {
                try {
                    $table->unique(['message_id', 'tenant_id', 'channel']);
                } catch (\Exception) {
                    // Constraint might already exist
                }

                try {
                    $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
                } catch (\Exception) {
                    // Foreign key might already exist
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints first
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_tenant_id']);
        });

        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        // Rename tables back to original names
        Schema::rename('tenant_groups', 'contact_groups');
        Schema::rename('tenant_imports', 'contact_imports');
        Schema::rename('tenants', 'contacts');

        // Reverse the unique constraint changes
        Schema::table('message_recipients', function (Blueprint $table) {
            try {
                $table->dropUnique(['message_id', 'tenant_id', 'channel']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            $table->unique(['message_id', 'contact_id', 'channel']);
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            try {
                $table->dropUnique(['tenant_id', 'group_id']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            $table->unique(['contact_id', 'group_id']);
        });

        // Reverse column renames and add back foreign keys
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_imports', function (Blueprint $table) {
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            $table->foreign('existing_contact_id')->references('id')->on('contacts')->onDelete('set null');
        });
    }
};
