<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update foreign key references in contact_imports table before renaming
        Schema::table('contact_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_contact_id']);
            $table->renameColumn('existing_contact_id', 'existing_tenant_id');
        });

        // Update foreign key references in contact_groups table before renaming
        Schema::table('contact_groups', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
            $table->renameColumn('contact_id', 'tenant_id');
        });

        // Update foreign key references in message_recipients table before renaming
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
            $table->renameColumn('contact_id', 'tenant_id');
        });

        // Update unique constraints before renaming
        Schema::table('contact_groups', function (Blueprint $table) {
            $table->dropUnique(['contact_id', 'group_id']);
            $table->unique(['tenant_id', 'group_id']);
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop existing unique constraint that includes contact_id
            $table->dropUnique(['message_id', 'contact_id', 'channel']);
            // Add new unique constraint with tenant_id
            $table->unique(['message_id', 'tenant_id', 'channel']);
        });

        // Now rename the tables
        Schema::rename('contacts', 'tenants');
        Schema::rename('contact_imports', 'tenant_imports');
        Schema::rename('contact_groups', 'tenant_groups');

        // Add back foreign key constraints with new table names
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->foreign('existing_tenant_id')->references('id')->on('tenants')->onDelete('set null');
        });

        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints first
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign(['existing_tenant_id']);
        });

        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        // Rename tables back to original names
        Schema::rename('tenant_groups', 'contact_groups');
        Schema::rename('tenant_imports', 'contact_imports');
        Schema::rename('tenants', 'contacts');

        // Reverse the unique constraint changes
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropUnique(['message_id', 'tenant_id', 'channel']);
            $table->unique(['message_id', 'contact_id', 'channel']);
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            $table->dropUnique(['tenant_id', 'group_id']);
            $table->unique(['contact_id', 'group_id']);
        });

        // Reverse column renames and add back foreign keys
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_groups', function (Blueprint $table) {
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        Schema::table('contact_imports', function (Blueprint $table) {
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
            $table->foreign('existing_contact_id')->references('id')->on('contacts')->onDelete('set null');
        });
    }
};
