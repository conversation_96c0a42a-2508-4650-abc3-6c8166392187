<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's find and drop any existing unique constraints on message_id, contact_id
        $indexes = DB::select("SHOW INDEX FROM message_recipients WHERE Non_unique = 0 AND Column_name IN ('message_id', 'contact_id')");

        foreach ($indexes as $index) {
            if ($index->Key_name !== 'PRIMARY') {
                try {
                    DB::statement("ALTER TABLE message_recipients DROP INDEX `{$index->Key_name}`");
                } catch (\Exception $e) {
                    // Continue if the index doesn't exist
                }
            }
        }

        // Add new unique constraint that includes channel
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->unique(['message_id', 'contact_id', 'channel'], 'message_recipients_message_contact_channel_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('message_recipients_message_contact_channel_unique');

            // Restore the old unique constraint
            $table->unique(['message_id', 'contact_id']);
        });
    }
};
