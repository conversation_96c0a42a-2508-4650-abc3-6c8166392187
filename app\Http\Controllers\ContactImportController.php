<?php

namespace App\Http\Controllers;

use App\Models\ContactImport;
use App\Models\Property;
use App\Services\ContactImportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ContactImportController extends Controller
{
    protected ContactImportService $importService;

    public function __construct(ContactImportService $importService)
    {
        $this->importService = $importService;
    }

    /**
     * Show the import index page
     */
    public function index()
    {
        $properties = Property::select('id', 'name')
            ->withCount('contacts')
            ->orderBy('name')
            ->get()
            ->map(function ($property) {
                return [
                    'id' => $property->id,
                    'name' => $property->name,
                    'contactCount' => $property->contacts_count
                ];
            });

        return Inertia::render('ContactImport/Index', [
            'properties' => $properties,
        ]);
    }

    /**
     * Handle file upload and analyze headers
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
            'property_id' => 'required|exists:properties,id'
        ]);

        try {
            $file = $request->file('file');
            $property = Property::findOrFail($request->property_id);

            // Store file temporarily
            $path = $file->store('temp-imports', 'local');
            $fullPath = storage_path('app/private/' . $path);

            // Read the first few rows to analyze headers
            $spreadsheet = IOFactory::load($fullPath);
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            // Get headers (first row)
            $headers = array_filter($data[0] ?? []);

            // Get sample data (next 5 rows)
            $sampleData = [];
            for ($i = 1; $i <= min(5, count($data) - 1); $i++) {
                if (!empty(array_filter($data[$i] ?? []))) {
                    $sampleData[] = $data[$i];
                }
            }

            $totalRows = count($data) - 1; // Exclude header row

            // Generate session ID
            $sessionId = Str::uuid();

            // Store file info in session
            session([
                'import_session_id' => $sessionId,
                'import_file_path' => $path,
                'import_property_id' => $property->id,
                'import_total_rows' => $totalRows
            ]);

            // Generate intelligent column mapping suggestions
            $suggestedMapping = $this->suggestColumnMapping($headers);

            return response()->json([
                'success' => true,
                'headers' => $headers,
                'sampleData' => $sampleData,
                'totalRows' => $totalRows,
                'property' => $property,
                'sessionId' => $sessionId,
                'suggestedMapping' => $suggestedMapping
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing file: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Process column mapping and create preview
     */
    public function mapColumns(Request $request)
    {
        $request->validate([
            'column_mapping' => 'required|array',
            'import_session' => 'required|string'
        ]);

        try {
            $sessionId = $request->import_session;
            $columnMapping = $request->column_mapping;

            // Validate required mappings
            if (empty($columnMapping['name']) && empty($columnMapping['mobile_phone'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Either name or mobile phone mapping is required'
                ], 422);
            }

            // Unit number is optional - no validation needed

            // Get file info from session
            $filePath = session('import_file_path');
            $propertyId = session('import_property_id');

            if (!$filePath || !$propertyId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session expired. Please start over.'
                ], 422);
            }

            $fullPath = storage_path('app/private/' . $filePath);

            // Read and process the Excel file
            $spreadsheet = IOFactory::load($fullPath);
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            // Remove header row
            $headers = array_shift($data);

            // Process each row
            $processedRows = [];
            foreach ($data as $index => $row) {
                if (empty(array_filter($row))) {
                    continue; // Skip empty rows
                }

                // Create associative array with headers
                $rowData = [];
                foreach ($headers as $colIndex => $header) {
                    $rowData[$header] = $row[$colIndex] ?? '';
                }

                $processed = $this->importService->processContactData($rowData, $columnMapping, $propertyId);
                $processed['raw_data'] = $rowData;
                $processed['row_number'] = $index + 2; // +2 because we removed header and Excel is 1-indexed

                $processedRows[] = $processed;
            }

            // Store processed data and create import records
            $stats = $this->importService->createImportRecords($processedRows, $sessionId);

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'sessionId' => $sessionId
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing data: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get preview data for review
     */
    public function preview(Request $request)
    {
        $sessionId = $request->get('import_session');

        try {
            $request->validate([
                'import_session' => 'required|string',
                'search' => 'nullable|string',
                'show_errors' => 'nullable|string|in:all,errors_only,valid_only,duplicates_only,new_only',
                'per_page' => 'nullable|integer|min:1|max:100',
                'sort_field' => 'nullable|string|in:first_name,last_name,email,mobile_phone,unit_number,created_at',
                'sort_direction' => 'nullable|string|in:asc,desc',
                'page' => 'nullable|integer|min:1'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Flash error message for validation failures
            $errorMessages = collect($e->errors())->flatten()->toArray();
            return redirect()->back()->with('error', 'Invalid request parameters: ' . implode(', ', $errorMessages));
        }

        // Get import data with pagination
        $perPage = $request->get('per_page', 10);
        $search = $request->get('search', '');
        $showErrors = $request->get('show_errors', 'all'); // 'all', 'errors_only', 'valid_only', 'duplicates_only', 'new_only'
        $sortField = $request->get('sort_field', 'first_name');
        $sortDirection = $request->get('sort_direction', 'asc');

        $query = ContactImport::where('session_id', $sessionId)
            ->with(['property', 'existingContact']);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('mobile_phone', 'like', "%{$search}%")
                  ->orWhere('unit_number', 'like', "%{$search}%");
            });
        }

        if ($showErrors === 'errors_only') {
            $query->where('has_errors', true);
        } elseif ($showErrors === 'valid_only') {
            $query->where('has_errors', false);
        } elseif ($showErrors === 'duplicates_only') {
            $query->where(function($q) {
                $q->whereNotNull('existing_contact_id')
                  ->orWhere('duplicate_action', 'skip');
            });
        } elseif ($showErrors === 'new_only') {
            $query->where('has_errors', false)
                  ->whereNull('existing_contact_id')
                  ->where(function($q) {
                      $q->whereNull('duplicate_action')
                        ->orWhere('duplicate_action', '!=', 'skip');
                  });
        }

        // Apply sorting
        $query->orderBy($sortField, $sortDirection);

        $imports = $query->paginate($perPage);

        // Get summary stats
        $stats = [
            'total' => ContactImport::where('session_id', $sessionId)->count(),
            'valid' => ContactImport::where('session_id', $sessionId)->where('has_errors', false)->count(),
            'errors' => ContactImport::where('session_id', $sessionId)->where('has_errors', true)->count(),
            'duplicates' => ContactImport::where('session_id', $sessionId)
                ->where(function($q) {
                    $q->whereNotNull('existing_contact_id')
                      ->orWhere('duplicate_action', 'skip');
                })->count(),
        ];



        return Inertia::render('ContactImport/Preview', [
            'imports' => $imports,
            'stats' => $stats,
            'sessionId' => $sessionId,
            'filters' => [
                'search' => $search,
                'show_errors' => $showErrors,
                'per_page' => $perPage,
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection
            ]
        ]);
    }

    /**
     * Execute the final import
     */
    public function import(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string'
        ]);

        try {
            $sessionId = $request->session_id;

            // Check if session has valid data
            $importCount = ContactImport::where('session_id', $sessionId)->count();
            if ($importCount === 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'No import data found. Please start over.'
                ], 422);
            }

            // Execute the import
            $stats = $this->importService->importContacts($sessionId);

            // Clean up session data
            session()->forget(['import_session_id', 'import_file_path', 'import_property_id', 'import_total_rows']);

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing contacts: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Cancel import and clean up
     */
    public function cancel(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string'
        ]);

        try {
            $sessionId = $request->session_id;

            // Delete temporary import records
            ContactImport::where('session_id', $sessionId)->delete();

            // Clean up uploaded file
            $filePath = session('import_file_path');
            if ($filePath && Storage::disk('local')->exists($filePath)) {
                Storage::disk('local')->delete($filePath);
            }

            // Clean up session data
            session()->forget(['import_session_id', 'import_file_path', 'import_property_id', 'import_total_rows']);

            return response()->json([
                'success' => true,
                'message' => 'Import cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling import: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Suggest column mappings based on header names
     */
    private function suggestColumnMapping(array $headers): array
    {
        $mapping = [];

        // Define mapping patterns for common header variations
        $patterns = [
            'name' => [
                'name of resident', 'resident name', 'name', 'full name', 'contact name'
            ],
            'unit_number' => [
                'unit #', 'unit', 'unit number', 'apartment', 'apt', 'suite'
            ],
            'mobile_phone' => [
                '1st contact', 'primary phone', 'phone', 'mobile', 'cell', 'contact 1'
            ],
            'secondary_mobile_phone' => [
                '2nd contact', 'secondary phone', 'phone 2', 'contact 2', 'alternate phone'
            ],
            'whatsapp_number' => [
                'whatsapp (1)', 'whatsapp 1', 'whatsapp', 'wa', 'whatsapp number'
            ],
            'secondary_whatsapp_number' => [
                'whatsapp (2)', 'whatsapp 2', 'wa 2', 'secondary whatsapp'
            ],
            'email' => [
                'email address', 'email', 'primary email', 'e-mail'
            ],
            'secondary_email' => [
                'email address(2)', 'email 2', 'secondary email', 'alternate email'
            ]
        ];

        // Try to match headers with patterns (avoid duplicate mappings)
        $usedHeaders = [];

        foreach ($patterns as $field => $fieldPatterns) {
            foreach ($headers as $header) {
                if (in_array($header, $usedHeaders)) {
                    continue; // Skip already used headers
                }

                $headerLower = strtolower(trim($header));

                foreach ($fieldPatterns as $pattern) {
                    if (str_contains($headerLower, strtolower($pattern))) {
                        $mapping[$field] = $header;
                        $usedHeaders[] = $header;
                        break 2; // Break to next field
                    }
                }
            }
        }

        return $mapping;
    }
}
