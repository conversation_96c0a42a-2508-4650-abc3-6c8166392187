<script setup lang="ts">
import { computed } from 'vue';
import { Head, useForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import AppLayout from '@/layouts/AppLayout.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, Building2, Phone, Mail, MessageSquare, HelpCircle, Clock } from 'lucide-vue-next';

import type { Tenant } from '@/types/tenant';
import type { Property } from '@/types/property';
import type { Group } from '@/types/group';

interface Props {
  tenant?: Tenant;
  properties: Property[];
  groups: Group[];
}

const props = defineProps<Props>();

// Form initialization
const form = useForm({
  id: props.tenant?.id || null,
  first_name: props.tenant?.first_name || '',
  last_name: props.tenant?.last_name || '',
  email: props.tenant?.email || '',
  secondary_email: props.tenant?.secondary_email || '',
  mobile_phone: props.tenant?.mobile_phone || '',
  secondary_mobile_phone: props.tenant?.secondary_mobile_phone || '',
  whatsapp_number: props.tenant?.whatsapp_number || '',
  secondary_whatsapp_number: props.tenant?.secondary_whatsapp_number || '',
  contact_sms: props.tenant?.contact_sms ?? true,
  contact_wa: props.tenant?.contact_wa ?? true,
  contact_email: props.tenant?.contact_email ?? true,
  property_id: props.tenant?.property_id || null,
  unit_number: props.tenant?.unit_number || '',
  status: props.tenant?.status ?? true,
  groups: props.tenant?.groups?.map(g => g.id) || [],
});

// Computed properties
const isEditing = computed(() => Boolean(props.tenant?.id));
const pageTitle = computed(() => isEditing.value ? 'Edit Tenant' : 'Add Tenant');

const breadcrumbs = [
  { title: 'Tenants', href: '/tenants' },
  { title: pageTitle.value, href: isEditing.value ? `/tenants/${form.id}/edit` : '/tenants/create' },
];

// Methods
const submit = () => {
  if (isEditing.value) {
    form.patch(route('tenants.update', { tenant: form.id }), {
      preserveScroll: true,
      onError: (errors) => {
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  } else {
    form.post(route('tenants.store'), {
      preserveScroll: true,
      onError: (errors) => {
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  }
};
</script>

<template>
  <Head :title="pageTitle" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="min-h-full flex-1 space-y-8 p-6 pb-16">
      <FlashAlert :errors="form.errors" />

      <!-- Page Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold tracking-tight">{{ pageTitle }}</h1>
          <p class="text-muted-foreground">
            {{ isEditing ? 'Update the tenant\'s information below.' : 'Enter the tenant details for the new tenant.' }}
          </p>
        </div>
      </div>

      <div class="grid gap-8 lg:grid-cols-3 max-w-7xl">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <Card class="card-primary">
            <CardHeader class="card-header-primary">
              <CardTitle class="card-title-primary">
                <User class="card-title-icon" />
                Tenant Information
              </CardTitle>
              <CardDescription>
                {{ isEditing ? 'Update the tenant\'s information below.' : 'Enter the tenant details for the new tenant.' }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="submit" class="form-section">
                <!-- Name Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="form-field">
                    <Label for="first_name" class="form-label">
                      <User class="form-label-icon" />
                      First Name
                      <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="first_name"
                      v-model="form.first_name"
                      required
                      placeholder="Enter first name"
                      class="form-input"
                    />
                  </div>

                  <div class="form-field">
                    <Label for="last_name" class="form-label">
                      <User class="form-label-icon" />
                      Last Name
                    </Label>
                    <Input
                      id="last_name"
                      v-model="form.last_name"
                      placeholder="Enter last name"
                      class="form-input"
                    />
                  </div>
                </div>

                <!-- Property and Unit -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="form-field">
                    <Label for="property_id" class="form-label">
                      <Building2 class="form-label-icon" />
                      Property
                      <span class="text-red-500">*</span>
                    </Label>
                    <Select v-model="form.property_id" required>
                      <SelectTrigger id="property_id" class="form-input">
                        <SelectValue placeholder="Select a property" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem v-for="property in properties" :key="property.id" :value="property.id.toString()">
                          {{ property.name }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div class="form-field">
                    <Label for="unit_number" class="form-label">
                      <Building2 class="form-label-icon" />
                      Unit Number
                    </Label>
                    <Input
                      id="unit_number"
                      v-model="form.unit_number"
                      placeholder="e.g., 101, A-5, etc."
                      class="form-input"
                    />
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="space-y-4">
                  <h3 class="text-lg font-medium">Contact Information</h3>
                  
                  <!-- Email Fields -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                      <Label for="email" class="form-label">
                        <Mail class="form-label-icon" />
                        Primary Email
                      </Label>
                      <Input
                        id="email"
                        v-model="form.email"
                        type="email"
                        placeholder="Enter email address"
                        class="form-input"
                      />
                    </div>

                    <div class="form-field">
                      <Label for="secondary_email" class="form-label">
                        <Mail class="form-label-icon" />
                        Secondary Email
                      </Label>
                      <Input
                        id="secondary_email"
                        v-model="form.secondary_email"
                        type="email"
                        placeholder="Enter secondary email"
                        class="form-input"
                      />
                    </div>
                  </div>

                  <!-- Phone Fields -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                      <Label for="mobile_phone" class="form-label">
                        <Phone class="form-label-icon" />
                        Primary Phone
                      </Label>
                      <Input
                        id="mobile_phone"
                        v-model="form.mobile_phone"
                        type="tel"
                        placeholder="Enter phone number"
                        class="form-input"
                      />
                    </div>

                    <div class="form-field">
                      <Label for="secondary_mobile_phone" class="form-label">
                        <Phone class="form-label-icon" />
                        Secondary Phone
                      </Label>
                      <Input
                        id="secondary_mobile_phone"
                        v-model="form.secondary_mobile_phone"
                        type="tel"
                        placeholder="Enter secondary phone"
                        class="form-input"
                      />
                    </div>
                  </div>

                  <!-- WhatsApp Fields -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                      <Label for="whatsapp_number" class="form-label">
                        <MessageSquare class="form-label-icon" />
                        Primary WhatsApp
                      </Label>
                      <Input
                        id="whatsapp_number"
                        v-model="form.whatsapp_number"
                        type="tel"
                        placeholder="Enter WhatsApp number"
                        class="form-input"
                      />
                    </div>

                    <div class="form-field">
                      <Label for="secondary_whatsapp_number" class="form-label">
                        <MessageSquare class="form-label-icon" />
                        Secondary WhatsApp
                      </Label>
                      <Input
                        id="secondary_whatsapp_number"
                        v-model="form.secondary_whatsapp_number"
                        type="tel"
                        placeholder="Enter secondary WhatsApp"
                        class="form-input"
                      />
                    </div>
                  </div>
                </div>

                <!-- Communication Preferences -->
                <div class="space-y-4">
                  <h3 class="text-lg font-medium">Communication Preferences</h3>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center space-x-2">
                      <Switch
                        id="contact_sms"
                        v-model:checked="form.contact_sms"
                      />
                      <Label for="contact_sms" class="text-sm font-medium">
                        SMS Messages
                      </Label>
                    </div>

                    <div class="flex items-center space-x-2">
                      <Switch
                        id="contact_email"
                        v-model:checked="form.contact_email"
                      />
                      <Label for="contact_email" class="text-sm font-medium">
                        Email Messages
                      </Label>
                    </div>

                    <div class="flex items-center space-x-2">
                      <Switch
                        id="contact_wa"
                        v-model:checked="form.contact_wa"
                      />
                      <Label for="contact_wa" class="text-sm font-medium">
                        WhatsApp Messages
                      </Label>
                    </div>
                  </div>
                </div>

                <!-- Status -->
                <div class="form-field">
                  <div class="flex items-center space-x-2">
                    <Switch
                      id="status"
                      v-model:checked="form.status"
                    />
                    <Label for="status" class="text-sm font-medium">
                      Active Status
                    </Label>
                  </div>
                  <p class="text-sm text-muted-foreground mt-1">
                    Inactive tenants will not receive messages and won't appear in recipient lists.
                  </p>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center gap-4 pt-6">
                  <Button
                    type="submit"
                    :disabled="form.processing"
                    class="btn-primary"
                  >
                    {{ isEditing ? 'Update Tenant' : 'Create Tenant' }}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    @click="router.visit(route('tenants.index'))"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Details Card -->
          <Card v-if="isEditing" class="card-secondary">
            <CardHeader class="card-header-secondary">
              <CardTitle class="card-title-secondary">
                <Clock class="card-title-icon" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Created</Label>
                <p class="text-sm">{{ props.tenant?.created_at }}</p>
              </div>
            </CardContent>
          </Card>

          <!-- Help Card -->
          <Card class="card-help">
            <CardHeader class="card-header-help">
              <CardTitle class="card-title-help">
                <HelpCircle class="card-title-icon" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-3 text-sm">
              <div>
                <strong>Required Fields:</strong> First name and property are required to create a tenant.
              </div>
              <div>
                <strong>Communication Preferences:</strong> Enable the channels you want to use to contact this tenant.
              </div>
              <div>
                <strong>Status:</strong> Inactive tenants won't receive messages or appear in recipient lists.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
