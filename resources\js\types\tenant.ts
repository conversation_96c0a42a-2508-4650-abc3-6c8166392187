import type { Property } from './property';
import type { Group } from './group';

export interface Tenant {
    id: number;
    first_name: string;
    last_name?: string;
    full_name: string;
    email?: string;
    secondary_email?: string;
    mobile_phone?: string;
    secondary_mobile_phone?: string;
    whatsapp_number?: string;
    secondary_whatsapp_number?: string;
    contact_sms: boolean;
    contact_wa: boolean;
    contact_email: boolean;
    property_id: number;
    property?: Property;
    unit_number?: string;
    status: boolean;
    groups?: Group[];
    created_at: string;
}

export interface TenantFilters {
    search: string;
    status?: string[];
    buildings?: number[];
    groups?: number[];
    per_page?: number;
}

export interface SortOptions {
    field: string;
    direction: 'asc' | 'desc';
}

export interface PaginatedTenants {
    data: Tenant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

// Legacy aliases for backward compatibility
export type Contact = Tenant;
export type ContactFilters = TenantFilters;
export type PaginatedContacts = PaginatedTenants;
