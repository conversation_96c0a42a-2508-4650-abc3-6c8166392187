<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Group extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'description',
        'color',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the tenants for the group.
     */
    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class, 'tenant_groups');
    }

    /**
     * Get the number of tenants associated with the group.
     *
     * @return int
     */
    public function getTenantsCountAttribute(): int
    {
        return $this->tenants()->count();
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use tenants() instead
     */
    public function contacts(): BelongsToMany
    {
        return $this->tenants();
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use getTenantsCountAttribute() instead
     */
    public function getContactsCountAttribute(): int
    {
        return $this->getTenantsCountAttribute();
    }
}
