<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\Property;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all property IDs
        $propertyIds = Property::pluck('id')->toArray();
        
        if (empty($propertyIds)) {
            $this->command->error('No properties found. Please run PropertySeeder first.');
            return;
        }

        // Create 200 tenants distributed across properties
        // Most tenants will be assigned to properties with even distribution
        // Some tenants (10%) will have no property assigned

        // Calculate how many tenants will be unassigned (10% of 200 = 20)
        $unassignedCount = 20;

        // Remaining tenants to be assigned to properties
        $assignedCount = 200 - $unassignedCount;

        // Create tenants with property assignments
        $this->command->info('Creating tenants with property assignments...');

        // Calculate base tenants per property and remainder
        $baseContactsPerProperty = (int) floor($assignedCount / count($propertyIds));
        $remainder = $assignedCount % count($propertyIds);
        
        // Distribute tenants evenly across properties
        foreach ($propertyIds as $index => $propertyId) {
            // Add one extra tenant to some properties to handle the remainder
            $extraContact = $index < $remainder ? 1 : 0;
            $contactsForThisProperty = $baseContactsPerProperty + $extraContact;
            
            // Create tenants for this property
            Tenant::factory()
                ->count($contactsForThisProperty)
                ->forProperty($propertyId)
                ->create();
        }

        // Create tenants without property assignments
        $this->command->info('Creating tenants without property assignments...');
        Tenant::factory()
            ->count($unassignedCount)
            ->withoutProperty()
            ->create();

        $this->command->info('Created 200 tenants for testing (180 assigned to properties, 20 unassigned)');
    }
}