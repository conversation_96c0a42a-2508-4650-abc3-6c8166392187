<template>
    <Head :title="`Message Details - ${message.subject || message.title}`" />
    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading
              :title="message.subject || message.title"
              :description="`View message details and delivery status`"
            />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 rounded-full" :class="getStatusDotClass(message.status)"></span>
                {{ message.status ? message.status.charAt(0).toUpperCase() + message.status.slice(1) : 'Unknown' }}
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 rounded-full" :class="getTypeDotClass(message.type || 'unknown')"></span>
                {{ message.type ? message.type.toUpperCase() : 'Unknown' }}
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-purple-500 rounded-full"></span>
                {{ deliveryStats.total }} recipients
              </span>
              <span v-if="message.user" class="flex items-center gap-1">
                <span class="h-2 w-2 bg-gray-500 rounded-full"></span>
                Created by {{ message.user.name }}
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <!-- Message Controls -->
            <div class="flex items-center gap-2">
              <IconButton v-if="canPauseMessage" @click="pauseMessage" variant="outline" tooltip="Pause sending"
                :disabled="isProcessing" class="h-11 w-11 shadow-sm">
                <Pause class="h-4 w-4" />
              </IconButton>

              <IconButton v-if="canResumeMessage" @click="resumeMessage" variant="outline" tooltip="Resume sending"
                :disabled="isProcessing" class="h-11 w-11 shadow-sm">
                <Play class="h-4 w-4" />
              </IconButton>

              <IconButton v-if="canCancelMessage" @click="cancelMessage" variant="destructive" tooltip="Cancel sending"
                :disabled="isProcessing" class="h-11 w-11 shadow-sm">
                <Square class="h-4 w-4" />
              </IconButton>

              <IconButton v-if="hasFailedRecipients" @click="retryFailedMessages" variant="outline"
                tooltip="Retry failed messages" :disabled="isProcessing" class="h-11 w-11 shadow-sm">
                <RotateCw class="h-4 w-4" />
              </IconButton>
            </div>

            <Button
              v-if="message.status === 'draft'"
              size="lg"
              class="shadow-lg hover:shadow-xl transition-all duration-200"
              asChild
            >
              <Link :href="`/messages/${message.id}/edit`">
                <Edit class="mr-2 h-5 w-5" />
                Edit Message
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert />

        <!-- Message Details -->
        <div class="grid gap-8 lg:grid-cols-3">
          <!-- Main Content -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Message Content -->
            <Card class="card-primary">
              <CardHeader class="card-header-primary">
                <CardTitle class="card-title-primary">
                  <MessageSquare class="card-title-icon" />
                  Message Content
                </CardTitle>
                <CardDescription>
                  {{ getContentDescription() }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <!-- Multi-channel content tabs -->
                <div v-if="hasMultipleChannels" class="space-y-4">
                  <!-- Content Tabs -->
                  <div class="border-b">
                    <nav class="flex space-x-8" aria-label="Content Tabs">
                      <button
                        v-if="hasEmailContent"
                        @click="activeContentTab = 'email'"
                        type="button"
                        :class="[
                          'py-2 px-1 border-b-2 font-medium text-sm cursor-pointer flex items-center gap-2',
                          activeContentTab === 'email'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                        ]"
                      >
                        <Mail class="h-4 w-4" />
                        Email
                      </button>
                      <button
                        v-if="hasSmsContent"
                        @click="activeContentTab = 'sms'"
                        type="button"
                        :class="[
                          'py-2 px-1 border-b-2 font-medium text-sm cursor-pointer flex items-center gap-2',
                          activeContentTab === 'sms'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                        ]"
                      >
                        <Phone class="h-4 w-4" />
                        SMS
                      </button>
                      <button
                        v-if="hasWhatsappContent"
                        @click="activeContentTab = 'whatsapp'"
                        type="button"
                        :class="[
                          'py-2 px-1 border-b-2 font-medium text-sm cursor-pointer flex items-center gap-2',
                          activeContentTab === 'whatsapp'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                        ]"
                      >
                        <MessageSquare class="h-4 w-4" />
                        WhatsApp
                      </button>
                    </nav>
                  </div>

                  <!-- Tab Content -->
                  <div class="py-4">
                    <!-- Email Content Tab -->
                    <div v-if="activeContentTab === 'email' && hasEmailContent" class="space-y-4">
                      <!-- Email Subject -->
                      <div v-if="message.subject" class="space-y-2">
                        <Label class="text-sm font-medium text-muted-foreground">Subject</Label>
                        <div class="p-3 bg-muted rounded-md">
                          <p class="font-medium">{{ message.subject }}</p>
                        </div>
                      </div>

                      <!-- Email Body -->
                      <div class="space-y-2">
                        <Label class="text-sm font-medium text-muted-foreground">Email Body</Label>
                        <div class="bg-muted rounded-md p-4 relative">
                          <div
                            class="prose prose-sm max-w-none dark:prose-invert"
                            :class="{ 'line-clamp-4': isEmailContentTruncated }"
                            v-html="sanitizedEmailContent"
                          ></div>
                          <div
                            v-if="isEmailContentTruncated"
                            class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent pointer-events-none"
                          ></div>
                        </div>
                        <Button
                          v-if="isEmailContentTruncated"
                          variant="link"
                          size="sm"
                          @click="showContentDialog = true; activeContentTab = 'email'"
                          class="text-xs h-auto p-0"
                        >
                          View Full Email Content
                        </Button>
                      </div>
                    </div>

                    <!-- SMS Content Tab -->
                    <div v-if="activeContentTab === 'sms' && hasSmsContent" class="space-y-4">
                      <div class="space-y-2">
                        <Label class="text-sm font-medium text-muted-foreground">SMS Message</Label>
                        <div class="bg-muted rounded-md p-4 relative">
                          <div class="whitespace-pre-wrap" :class="{ 'line-clamp-4': isSmsContentTruncated }">{{ message.sms_content }}</div>
                          <div
                            v-if="isSmsContentTruncated"
                            class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent pointer-events-none"
                          ></div>
                        </div>
                        <Button
                          v-if="isSmsContentTruncated"
                          variant="link"
                          size="sm"
                          @click="showContentDialog = true; activeContentTab = 'sms'"
                          class="text-xs h-auto p-0"
                        >
                          View Full SMS Content
                        </Button>
                      </div>
                    </div>

                    <!-- WhatsApp Content Tab -->
                    <div v-if="activeContentTab === 'whatsapp' && hasWhatsappContent" class="space-y-4">
                      <div class="space-y-2">
                        <Label class="text-sm font-medium text-muted-foreground">WhatsApp Message</Label>
                        <div class="bg-muted rounded-md p-4 relative">
                          <div
                            class="whitespace-pre-wrap"
                            :class="{ 'line-clamp-4': isWhatsappContentTruncated }"
                            v-html="formattedWhatsAppContent"
                          ></div>
                          <div
                            v-if="isWhatsappContentTruncated"
                            class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent pointer-events-none"
                          ></div>
                        </div>
                        <Button
                          v-if="isWhatsappContentTruncated"
                          variant="link"
                          size="sm"
                          @click="showContentDialog = true; activeContentTab = 'whatsapp'"
                          class="text-xs h-auto p-0"
                        >
                          View Full WhatsApp Content
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Legacy single-channel content display -->
                <div v-else class="space-y-4">
                  <!-- Subject (for email) -->
                  <div v-if="message.subject && message.type === 'email'" class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">Subject</Label>
                    <div class="p-3 bg-muted rounded-md">
                      <p class="font-medium">{{ message.subject }}</p>
                    </div>
                  </div>

                  <!-- Content -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <Label class="text-sm font-medium text-muted-foreground">Content</Label>
                      <Button
                        v-if="isContentTruncated"
                        variant="link"
                        size="sm"
                        @click="showContentDialog = true"
                        class="text-xs h-auto p-0"
                      >
                        View Full Content
                      </Button>
                    </div>
                    <div class="bg-muted rounded-md p-4 relative">
                      <!-- SMS Content -->
                      <div v-if="message.type === 'sms'" class="whitespace-pre-wrap">
                        {{ truncatedContent }}
                      </div>

                      <!-- Email Content (HTML rendered) -->
                      <div
                        v-else-if="message.type === 'email'"
                        class="prose prose-sm max-w-none dark:prose-invert"
                        :class="{ 'line-clamp-4': isContentTruncated }"
                        v-html="sanitizedContent"
                      ></div>

                      <!-- WhatsApp Content (formatted) -->
                      <div
                        v-else-if="message.type === 'whatsapp'"
                        class="whitespace-pre-wrap"
                        :class="{ 'line-clamp-4': isContentTruncated }"
                        v-html="formattedWhatsAppContent"
                      ></div>

                      <!-- Fallback for other types -->
                      <div v-else class="whitespace-pre-wrap">
                        {{ truncatedContent }}
                      </div>

                      <!-- Gradient overlay for truncated content -->
                      <div
                        v-if="isContentTruncated"
                        class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent pointer-events-none"
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Delivery Statistics -->
            <Card class="card-secondary">
              <CardHeader class="card-header-secondary">
                <CardTitle class="card-title-secondary">
                  <BarChart class="card-title-icon" />
                  Delivery Statistics
                </CardTitle>
                <CardDescription>
                  Real-time delivery status and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div class="text-center p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <Users class="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p class="text-2xl font-bold">{{ deliveryStats.total }}</p>
                    <p class="text-sm text-muted-foreground">Total Recipients</p>
                  </div>

                  <div class="text-center p-4 rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                    <CheckCircle class="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <p class="text-2xl font-bold text-green-600">{{ deliveryStats.sent }}</p>
                    <p class="text-sm text-green-700 dark:text-green-400">Sent Successfully</p>
                  </div>

                  <div class="text-center p-4 rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
                    <XCircle class="h-8 w-8 text-red-600 mx-auto mb-2" />
                    <p class="text-2xl font-bold text-red-600">{{ deliveryStats.failed }}</p>
                    <p class="text-sm text-red-700 dark:text-red-400">Failed</p>
                  </div>

                  <div class="text-center p-4 rounded-lg border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
                    <Clock class="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <p class="text-2xl font-bold text-blue-600">{{ deliveryStats.pending }}</p>
                    <p class="text-sm text-blue-700 dark:text-blue-400">Pending</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Details -->
            <Card class="sidebar-card-details">
              <CardHeader class="sidebar-card-header">
                <CardTitle class="sidebar-card-title">
                  <Info class="h-4 w-4" />
                  Details
                </CardTitle>
              </CardHeader>
              <CardContent class="sidebar-card-content">
                <div class="space-y-3">
                  <!-- Message Channels -->
                  <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <MessageSquare class="h-4 w-4" />
                      Channels
                    </span>
                    <div v-if="hasMultipleChannels" class="flex items-center gap-1">
                      <div v-for="channel in message.channels" :key="channel" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="getMessageTypeClass(channel)">
                        <div class="h-1.5 w-1.5 rounded-full" :class="getTypeDotClass(channel)"></div>
                        {{ channel ? channel.toUpperCase() : 'Unknown' }}
                      </div>
                    </div>
                    <div v-else-if="message.type" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="getMessageTypeClass(message.type)">
                      <div class="h-1.5 w-1.5 rounded-full" :class="getTypeDotClass(message.type)"></div>
                      {{ message.type ? message.type.toUpperCase() : 'Unknown' }}
                    </div>
                    <span v-else class="text-sm text-muted-foreground">None</span>
                  </div>

                  <!-- Status -->
                  <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Clock class="h-4 w-4" />
                      Status
                    </span>
                    <div class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="getStatusClass(message.status)">
                      <div class="h-1.5 w-1.5 rounded-full" :class="getStatusDotClass(message.status)"></div>
                      {{ message.status ? message.status.charAt(0).toUpperCase() + message.status.slice(1) : 'Unknown' }}
                    </div>
                  </div>

                  <!-- Created Date -->
                  <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Calendar class="h-4 w-4" />
                      Created
                    </span>
                    <span class="text-sm font-medium"><DateTime :date="message.created_at" type="absolute" /></span>
                  </div>

                  <!-- Created By -->
                  <div v-if="message.user" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <User class="h-4 w-4" />
                      Created By
                    </span>
                    <span class="text-sm font-medium">{{ message.user.name }}</span>
                  </div>

                  <!-- Recipients Count -->
                  <div class="flex items-center justify-between py-2">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Users class="h-4 w-4" />
                      Recipients
                    </span>
                    <span class="text-sm font-medium">{{ deliveryStats.total }}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Progress Bar -->
        <div v-if="message.status === 'sending'" class="bg-card rounded-lg border p-4">
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Label class="text-sm font-medium">Sending Progress</Label>
              <span class="text-sm text-muted-foreground">{{ deliveryStats.progress }}%</span>
            </div>
            <div class="w-full bg-muted rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: deliveryStats.progress + '%' }"></div>
            </div>
            <p class="text-sm text-muted-foreground">
              {{ deliveryStats.sent }} of {{ deliveryStats.total }} messages sent
            </p>
          </div>
        </div>

        <!-- Tabs for Recipients and Logs -->
        <div class="bg-card rounded-lg border">
          <div class="border-b">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
              <button @click="activeTab = 'recipients'" :class="[
                'py-4 px-1 border-b-2 font-medium text-sm cursor-pointer',
                activeTab === 'recipients'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              ]">
                Recipients ({{ message.recipient_count }})
              </button>
              <button @click="activeTab = 'logs'" :class="[
                'py-4 px-1 border-b-2 font-medium text-sm cursor-pointer',
                activeTab === 'logs'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              ]">
                Activity Logs
              </button>
            </nav>
          </div>

          <!-- Recipients Tab -->
          <div v-if="activeTab === 'recipients'" class="p-6">
            <div class="space-y-4">
              <!-- Recipients Filters -->
              <div class="flex items-center gap-2">
                <div class="relative flex-1 max-w-sm">
                  <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Search recipients..." class="pl-8" v-model="recipientSearch" />
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="outline">
                      <Filter class="mr-2 h-4 w-4" />
                      Status
                      <Badge v-if="selectedRecipientStatus" variant="secondary" class="ml-2">
                        {{ selectedRecipientStatus ? selectedRecipientStatus.toUpperCase() : 'Unknown' }}
                      </Badge>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Filter by status</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div class="p-2 space-y-1">
                      <div v-for="status in recipientStatuses" :key="status.value">
                        <Checkbox :id="status.value" :modelValue="selectedRecipientStatus === status.value"
                          @update:modelValue="(checked) => selectedRecipientStatus = checked ? status.value : null" />
                        <label :for="status.value" class="ml-2 text-sm cursor-pointer">
                          {{ status.label }}
                        </label>
                      </div>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <!-- Recipients Table -->
              <div class="border rounded-lg">
                <UITable>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Contact</TableHead>
                      <TableHead>Recipient</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Sent At</TableHead>
                      <TableHead>Error</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow v-if="!filteredRecipients.length">
                      <TableCell colspan="7" class="h-24 text-center text-muted-foreground">
                        No recipients found
                      </TableCell>
                    </TableRow>
                    <TableRow v-for="recipient in paginatedRecipients" :key="recipient.id">
                      <TableCell>
                        <div>
                          <p class="font-medium">{{ recipient.contact?.first_name }} {{ recipient.contact?.last_name }}
                          </p>
                          <p class="text-sm text-muted-foreground">{{ recipient.contact?.property?.name }}</p>
                        </div>
                      </TableCell>
                      <TableCell>{{ recipient.recipient_value }}</TableCell>
                      <TableCell>
                        <Badge :class="getMessageTypeClass(recipient.channel || 'unknown')">
                          {{ recipient.channel ? recipient.channel.toUpperCase() : 'UNKNOWN' }}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge :class="getRecipientStatusClass(recipient.status)">
                          {{ recipient.status ? recipient.status.toUpperCase() : 'Unknown' }}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DateTime v-if="recipient.sent_at" :date="recipient.sent_at" />
                        <span v-else class="text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell>
                        <span v-if="recipient.error_message" class="text-sm text-red-600">
                          <span class="inline-block ">{{ truncateText(recipient.error_message, 50) }}</span>
                          <Button v-if="recipient.error_message.length > 50" variant="link" class="text-xs h-auto p-0 ml-1"
                            @click="showDetails('Recipient Error', recipient.error_message, {
                              recipient: recipient.recipient_value,
                              status: recipient.status,
                              sent_at: recipient.sent_at
                            })">
                            Show more
                          </Button>
                        </span>
                        <span v-else class="text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell>
                        <IconButton v-if="recipient.status === 'failed'" @click="retryRecipient(recipient)"
                          variant="outline" tooltip="Retry" size="sm">
                          <RotateCw class="h-3 w-3" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </UITable>
              </div>

              <!-- Recipients Pagination -->
              <div v-if="totalRecipientPages > 1" class="flex items-center justify-center space-x-2">
                <Button variant="outline" size="sm" @click="recipientPage = Math.max(1, recipientPage - 1)"
                  :disabled="recipientPage === 1">
                  Previous
                </Button>
                <span class="text-sm text-muted-foreground">
                  Page {{ recipientPage }} of {{ totalRecipientPages }}
                </span>
                <Button variant="outline" size="sm"
                  @click="recipientPage = Math.min(totalRecipientPages, recipientPage + 1)"
                  :disabled="recipientPage === totalRecipientPages">
                  Next
                </Button>
              </div>
            </div>
          </div>

          <!-- Activity Logs Tab -->
          <div v-if="activeTab === 'logs'" class="p-6">
            <div class="space-y-4">
              <!-- Header with Filters and Pagination -->
              <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <!-- Log Filters -->
                <div class="flex items-center gap-2">
                  <div class="relative flex-1 max-w-sm">
                    <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input type="search" placeholder="Search logs..." class="pl-8" v-model="logSearch" />
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                      <Button variant="outline">
                        <Filter class="mr-2 h-4 w-4" />
                        Event Type
                        <Badge v-if="selectedLogEvent" variant="secondary" class="ml-2">
                          {{ selectedLogEvent ? selectedLogEvent.toUpperCase() : 'Unknown' }}
                        </Badge>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuLabel>Filter by event</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <div class="p-2 space-y-1">
                        <div v-for="event in logEventTypes" :key="event.value">
                          <Checkbox :id="'event-' + event.value" :modelValue="selectedLogEvent === event.value"
                            @update:modelValue="(checked) => selectedLogEvent = checked ? event.value : null" />
                          <label :for="'event-' + event.value" class="ml-2 text-sm cursor-pointer">
                            {{ event.label }}
                          </label>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <!-- Pagination Controls -->
                <div v-if="!logSearch && !selectedLogEvent && logs.last_page > 1" class="flex items-center gap-4">
                  <div class="flex items-center gap-2">
                    <Button variant="outline" size="sm" :disabled="logs.current_page === 1"
                      @click="goToPage(logs.current_page - 1)">
                      Previous
                    </Button>
                    <span class="text-sm text-muted-foreground whitespace-nowrap">
                      Page {{ logs.current_page }} of {{ logs.last_page }}
                    </span>
                    <Button variant="outline" size="sm" :disabled="logs.current_page === logs.last_page"
                      @click="goToPage(logs.current_page + 1)">
                      Next
                    </Button>
                  </div>
                  <div class="text-sm text-muted-foreground hidden lg:block whitespace-nowrap">
                    {{ logs.total }} logs
                  </div>
                </div>
              </div>

              <!-- Timeline -->
              <div class="relative">
                <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-muted"></div>
                <div class="space-y-6">
                  <div v-for="log in filteredLogs" :key="log.id" class="relative pl-8">
                    <div class="absolute left-0 top-5 w-8 flex items-center justify-center">
                      <div :class="getEventIconClass(log.event)" class="w-3 h-3 rounded-full"></div>
                    </div>
                    <div class="bg-card p-4 rounded-lg border space-y-2">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                          <Badge :class="getEventClass(log.event)">
                            {{ log.event ? log.event.toUpperCase() : 'Unknown' }}
                          </Badge>
                          <Badge v-if="log.message_recipient_id && getLogMessageType(log)" :class="getMessageTypeClass(getLogMessageType(log) || 'unknown')" class="text-xs">
                            {{ getLogMessageType(log)?.toUpperCase() }}
                          </Badge>
                          <span v-if="log.message_recipient_id" class="text-xs text-muted-foreground">
                            Recipient: {{ getRecipientValue(log.message_recipient_id) }}
                          </span>
                        </div>
                        <DateTime :date="log.created_at" class="text-xs text-muted-foreground" />
                      </div>
                      <div class="text-sm text-muted-foreground">
                        <p class="inline-block whitespace-pre-wrap">{{ truncateText(log.description, 100) }}</p>
                        <Button v-if="log.description.length > 100 || log.data" variant="link"
                          class="text-xs h-auto p-0 ml-1" @click="showLogDetails(log)">
                          Show more
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- No Results -->
              <div v-if="filteredLogs.length === 0" class="text-center py-8">
                <p class="text-muted-foreground">No logs found</p>
                <button v-if="hasLogFilters" @click="resetLogFilters"
                  class="text-primary underline-offset-4 hover:underline mt-2">
                  Reset filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>

    <!-- Details Dialog -->
    <Dialog v-model:open="showDetailsDialog">
      <DialogContent class="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{{ detailsData.title }}</DialogTitle>
        </DialogHeader>
        <div class="flex-1 overflow-y-auto space-y-4 py-4">
          <div>
            <Label>Message</Label>
            <p class="text-sm whitespace-pre-wrap mt-1 p-2 bg-muted rounded-md">{{ detailsData.message }}</p>
          </div>
          <div v-if="detailsData.data">
            <Label>Additional Details</Label>
            <pre class="mt-1 p-2 bg-muted rounded-md text-xs overflow-x-auto">{{ JSON.stringify(detailsData.data, null, 2)
            }}</pre>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showDetailsDialog = false">Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Content Dialog -->
    <Dialog v-model:open="showContentDialog">
      <DialogContent class="!max-w-4xl max-h-[85vh] flex flex-col">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <Mail v-if="activeContentTab === 'email'" class="h-5 w-5 text-amber-600" />
            <Phone v-else-if="activeContentTab === 'sms'" class="h-5 w-5 text-blue-600" />
            <MessageSquare v-else-if="activeContentTab === 'whatsapp'" class="h-5 w-5 text-green-600" />
            <MessageSquare v-else class="h-5 w-5" />
            Full {{ activeContentTab ? activeContentTab.toLowerCase() : 'message' }} content
          </DialogTitle>
          <p class="text-sm text-muted-foreground">{{ getSelectedChannelDescription() }}</p>
        </DialogHeader>
        <div class="flex-1 overflow-y-auto py-4">
          <!-- Multi-channel content - show only selected channel -->
          <div v-if="hasMultipleChannels">
            <!-- Email Content -->
            <div v-if="activeContentTab === 'email' && hasEmailContent">
            
              <div v-if="message.subject" class="mb-6">
                <Label class="text-sm text-muted-foreground mb-2 block">Subject Line</Label>
                <p class="font-medium border rounded-lg p-3 px-6 bg-muted">{{ message.subject }}</p>
              </div>

              <div>
                <Label class="text-sm text-muted-foreground mb-2 block">Email Body</Label>
                <div class="prose prose-sm max-w-none dark:prose-invert border rounded-lg p-6 bg-muted leading-relaxed" v-html="sanitizedEmailContent"></div>
              </div>
            </div>

            <!-- WhatsApp Content -->
            <div v-if="activeContentTab === 'whatsapp' && hasWhatsappContent" class="border rounded-lg p-6  bg-muted">
                <div class="leading-relaxed" v-html="formattedWhatsAppContent"></div>
            </div>
          </div>

          <!-- Legacy single-channel content -->
          <div v-else class="border rounded-lg p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="p-2 rounded-full" :class="{
                'bg-blue-500': message.type === 'sms',
                'bg-amber-500': message.type === 'email',
                'bg-green-500': message.type === 'whatsapp',
                'bg-gray-500': !message.type
              }">
                <Phone v-if="message.type === 'sms'" class="h-5 w-5 text-white" />
                <Mail v-else-if="message.type === 'email'" class="h-5 w-5 text-white" />
                <MessageSquare v-else-if="message.type === 'whatsapp'" class="h-5 w-5 text-white" />
                <MessageSquare v-else class="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 class="text-lg font-semibold">{{ (message.type || 'Message').toUpperCase() }} Content</h3>
                <p class="text-sm text-muted-foreground">Legacy single-channel message</p>
              </div>
            </div>

            <!-- SMS Content -->
            <div v-if="message.type === 'sms'" class="p-6 bg-muted rounded-lg">
              <div class="whitespace-pre-wrap font-mono text-sm leading-relaxed">{{ message.content }}</div>
            </div>

            <!-- Email Content (HTML rendered) -->
            <div
              v-else-if="message.type === 'email'"
              class="prose prose-sm max-w-none dark:prose-invert p-6 bg-muted rounded-lg"
              v-html="sanitizedContent"
            ></div>

            <!-- WhatsApp Content (formatted) -->
            <div
              v-else-if="message.type === 'whatsapp'"
              class="p-6 bg-muted rounded-lg leading-relaxed"
              v-html="formattedWhatsAppContent"
            ></div>

            <!-- Fallback for other types -->
            <div v-else class="p-6 bg-muted rounded-lg">
              <div class="whitespace-pre-wrap font-mono text-sm leading-relaxed">{{ message.content }}</div>
            </div>
          </div>
        </div>
        <DialogFooter class="flex items-center justify-between">
          <Button @click="showContentDialog = false">Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import {
  Users, CheckCircle, XCircle, Clock, Search, Filter,
  Pause, Play, Square, RotateCw, MessageSquare, BarChart,
  Info, Calendar, User, Edit, Phone, Mail
} from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import DateTime from '@/components/ui/DateTime.vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import IconButton from '@/components/ui/IconButton.vue';
import type { Message, MessageRecipient, DeliveryStats } from '@/types/message';
import { truncateText } from '@/utils/text';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

interface Props {
  message: Message & {
    recipients: MessageRecipient[];
  };
  logs: {
    data: Array<{
      id: number;
      event: string;
      description: string;
      message_recipient_id?: number;
      data?: any;
      created_at: string;
    }>;
    current_page: number;
    last_page: number;
    total: number;
    per_page: number;
  };
  deliveryStats: DeliveryStats;
  refreshInterval?: number;
}

const props = defineProps<Props>();

// State
const activeTab = ref<'recipients' | 'logs'>('recipients');
const recipientSearch = ref('');
const selectedRecipientStatus = ref<string | null>(null);
const recipientPage = ref(1);
const recipientsPerPage = 20;
const isProcessing = ref(false);
const showDetailsDialog = ref(false);
const detailsData = ref<{
  title: string;
  message: string;
  data?: any;
}>({
  title: '',
  message: '',
  data: null
});

const logSearch = ref('');
const selectedLogEvent = ref<string | null>(null);

// Add refresh functionality
const refreshTimer = ref<number | null>(null);
const isRefreshing = ref(false);

// Content dialog state
const showContentDialog = ref(false);

// Initialize activeContentTab with a default value, will be set properly in onMounted
const activeContentTab = ref<'sms' | 'email' | 'whatsapp'>('sms');

// Constants
const breadcrumbs = [
  { title: 'Messages', href: '/messages' },
  { title: 'Message Details', href: `/messages/${props.message.id}` },
];

const recipientStatuses = [
  { label: 'Pending', value: 'pending' },
  { label: 'Sent', value: 'sent' },
  { label: 'Failed', value: 'failed' },
  { label: 'Delivered', value: 'delivered' },
  { label: 'Read', value: 'read' },
];

const logEventTypes = [
  { label: 'Created', value: 'created' },
  { label: 'Draft Saved', value: 'draft_saved' },
  { label: 'Draft Updated', value: 'draft_updated' },
  { label: 'Updated', value: 'updated' },
  { label: 'Queued', value: 'queued' },
  { label: 'Sending Started', value: 'sending_started' },
  { label: 'Recipient Sent', value: 'recipient_sent' },
  { label: 'Recipient Failed', value: 'recipient_failed' },
  { label: 'Recipient Skipped', value: 'recipient_skipped' },
  { label: 'Sending Completed', value: 'sending_completed' },
  { label: 'Sending Failed', value: 'sending_failed' },
  { label: 'Status Update', value: 'status_update' },
  { label: 'Paused', value: 'paused' },
  { label: 'Resumed', value: 'resumed' },
  { label: 'Cancelled', value: 'cancelled' },
  { label: 'Retry', value: 'retry' },
  { label: 'Retry Initiated', value: 'retry_initiated' },
  { label: 'Retry Recipient Started', value: 'retry_recipient_started' },
  { label: 'Retry Recipient Success', value: 'retry_recipient_success' },
  { label: 'Retry Recipient Failed', value: 'retry_recipient_failed' },
];



// Computed
const canPauseMessage = computed(() => {
  return props.message.status === 'sending' || props.message.status === 'queued';
});

const canResumeMessage = computed(() => {
  return props.message.status === 'paused';
});

const canCancelMessage = computed(() => {
  return ['queued', 'sending', 'paused'].includes(props.message.status);
});

const hasFailedRecipients = computed(() => {
  return props.deliveryStats.failed > 0;
});

// Content handling computed properties
const isContentTruncated = computed(() => {
  const content = props.message.content || '';
  const lines = content.split('\n').length;
  return lines > 4 || content.length > 300;
});

const truncatedContent = computed(() => {
  const content = props.message.content || '';
  const lines = content.split('\n');
  if (lines.length > 4) {
    return lines.slice(0, 4).join('\n') + '...';
  }
  if (content.length > 300) {
    return content.substring(0, 300) + '...';
  }
  return content;
});

const sanitizedContent = computed(() => {
  // Basic HTML sanitization for email content
  const content = props.message.content || '';
  // Remove script tags and other potentially dangerous elements
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '');
});

const formattedWhatsAppContent = computed(() => {
  const content = props.message.whatsapp_content || props.message.content || '';
  // Convert WhatsApp markdown to HTML for display
  return content
    .replace(/\*([^*]+)\*/g, '<strong>$1</strong>')
    .replace(/_([^_]+)_/g, '<em>$1</em>')
    .replace(/~([^~]+)~/g, '<del>$1</del>')
    .replace(/```([^`]+)```/g, '<code class="bg-muted px-1 rounded">$1</code>')
    .replace(/\n/g, '<br>');
});

// Multi-channel computed properties
const hasMultipleChannels = computed(() => {
  return props.message.channels && props.message.channels.length > 0;
});

const hasEmailContent = computed(() => {
  return hasMultipleChannels.value &&
         props.message.channels?.includes('email') &&
         (props.message.email_content || props.message.subject);
});

const hasSmsContent = computed(() => {
  return hasMultipleChannels.value &&
         props.message.channels?.includes('sms') &&
         props.message.sms_content;
});

const hasWhatsappContent = computed(() => {
  return hasMultipleChannels.value &&
         props.message.channels?.includes('whatsapp') &&
         props.message.whatsapp_content;
});

// Default content tab based on available channels
const defaultContentTab = computed(() => {
  if (hasEmailContent.value) return 'email';
  if (hasSmsContent.value) return 'sms';
  if (hasWhatsappContent.value) return 'whatsapp';
  return 'sms';
});

// Watch for changes in available content and update active tab
watch(defaultContentTab, (newTab: 'sms' | 'email' | 'whatsapp') => {
  activeContentTab.value = newTab;
}, { immediate: true });

const sanitizedEmailContent = computed(() => {
  const content = props.message.email_content || '';
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '');
});

const isEmailContentTruncated = computed(() => {
  const content = props.message.email_content || '';
  const lines = content.split('\n').length;
  return lines > 4 || content.length > 300;
});

const isSmsContentTruncated = computed(() => {
  const content = props.message.sms_content || '';
  const lines = content.split('\n').length;
  return lines > 4 || content.length > 300;
});

const isWhatsappContentTruncated = computed(() => {
  const content = props.message.whatsapp_content || '';
  const lines = content.split('\n').length;
  return lines > 4 || content.length > 300;
});

const getContentDescription = () => {
  if (hasMultipleChannels.value) {
    const channels = props.message.channels || [];
    const channelNames = channels.map(c => c ? c.toUpperCase() : 'Unknown').join(', ');
    return `Multi-channel message content for: ${channelNames}`;
  }

  if (props.message.type === 'email') {
    return 'Email content with subject and body';
  }

  return `${(props.message.type || 'Message').toUpperCase()} message content`;
};

const getSelectedChannelDescription = () => {
  switch (activeContentTab.value) {
    case 'email':
      return 'Rich HTML email message with subject and body';
    case 'sms':
      return 'Plain text message for mobile devices';
    case 'whatsapp':
      return 'Formatted message with WhatsApp styling';
    default:
      return 'Message content';
  }
};

const filteredRecipients = computed(() => {
  let recipients = props.message.recipients || [];

  // Filter by search
  if (recipientSearch.value) {
    const search = recipientSearch.value.toLowerCase();
    recipients = recipients.filter(recipient =>
      recipient.tenant?.first_name?.toLowerCase().includes(search) ||
      recipient.tenant?.last_name?.toLowerCase().includes(search) ||
      recipient.recipient_value.toLowerCase().includes(search)
    );
  }

  // Filter by status
  if (selectedRecipientStatus.value) {
    recipients = recipients.filter(recipient =>
      recipient.status === selectedRecipientStatus.value
    );
  }

  return recipients;
});

const totalRecipientPages = computed(() => {
  return Math.ceil(filteredRecipients.value.length / recipientsPerPage);
});

const paginatedRecipients = computed(() => {
  const start = (recipientPage.value - 1) * recipientsPerPage;
  const end = start + recipientsPerPage;
  return filteredRecipients.value.slice(start, end);
});

const filteredLogs = computed(() => {
  let logs = props.logs.data;

  if (logSearch.value) {
    const searchLower = logSearch.value.toLowerCase();
    logs = logs.filter(log =>
      log.description.toLowerCase().includes(searchLower) ||
      log.event.toLowerCase().includes(searchLower)
    );
  }

  if (selectedLogEvent.value) {
    logs = logs.filter(log => log.event === selectedLogEvent.value);
  }

  return logs;
});

const hasLogFilters = computed(() => {
  return logSearch.value || selectedLogEvent.value;
});

const resetLogFilters = () => {
  logSearch.value = '';
  selectedLogEvent.value = null;
};

const getEventClass = (event: string) => {
  switch (event) {
    // Creation and Draft Events
    case 'created':
      return 'bg-slate-100 text-slate-700 dark:bg-slate-900/30 dark:text-slate-300';
    case 'draft_saved':
      return 'bg-stone-100 text-stone-700 dark:bg-stone-900/30 dark:text-stone-300';
    case 'draft_updated':
      return 'bg-zinc-100 text-zinc-700 dark:bg-zinc-900/30 dark:text-zinc-300';
    case 'updated':
      return 'bg-slate-100 text-slate-700 dark:bg-slate-900/30 dark:text-slate-300';

    // Queue and Sending Events
    case 'queued':
      return 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300';
    case 'sending_started':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300';
    case 'resumed':
      return 'bg-cyan-100 text-cyan-700 dark:bg-cyan-900/30 dark:text-cyan-300';

    // Success Events
    case 'recipient_sent':
      return 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300';
    case 'sending_completed':
      return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
    case 'retry_recipient_success':
      return 'bg-teal-100 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300';

    // Failure Events
    case 'recipient_failed':
      return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300';
    case 'sending_failed':
      return 'bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300';
    case 'retry_recipient_failed':
      return 'bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300';

    // Warning/Skip Events
    case 'paused':
      return 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300';
    case 'recipient_skipped':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';

    // Control Events
    case 'cancelled':
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
    case 'status_update':
      return 'bg-sky-100 text-sky-700 dark:bg-sky-900/30 dark:text-sky-300';

    // Retry Events
    case 'retry':
      return 'bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300';
    case 'retry_initiated':
      return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300';
    case 'retry_recipient_started':
      return 'bg-fuchsia-100 text-fuchsia-700 dark:bg-fuchsia-900/30 dark:text-fuchsia-300';

    default:
      return 'bg-neutral-100 text-neutral-700 dark:bg-neutral-900/30 dark:text-neutral-300';
  }
};

const getEventIconClass = (event: string) => {
  switch (event) {
    // Creation and Draft Events
    case 'created':
      return 'bg-slate-500 dark:bg-slate-400';
    case 'draft_saved':
      return 'bg-stone-500 dark:bg-stone-400';
    case 'draft_updated':
      return 'bg-zinc-500 dark:bg-zinc-400';
    case 'updated':
      return 'bg-slate-500 dark:bg-slate-400';

    // Queue and Sending Events
    case 'queued':
      return 'bg-indigo-500 dark:bg-indigo-400';
    case 'sending_started':
      return 'bg-blue-500 dark:bg-blue-400';
    case 'resumed':
      return 'bg-cyan-500 dark:bg-cyan-400';

    // Success Events
    case 'recipient_sent':
      return 'bg-emerald-500 dark:bg-emerald-400';
    case 'sending_completed':
      return 'bg-green-500 dark:bg-green-400';
    case 'retry_recipient_success':
      return 'bg-teal-500 dark:bg-teal-400';

    // Failure Events
    case 'recipient_failed':
      return 'bg-red-500 dark:bg-red-400';
    case 'sending_failed':
      return 'bg-rose-500 dark:bg-rose-400';
    case 'retry_recipient_failed':
      return 'bg-pink-500 dark:bg-pink-400';

    // Warning/Skip Events
    case 'paused':
      return 'bg-amber-500 dark:bg-amber-400';
    case 'recipient_skipped':
      return 'bg-yellow-500 dark:bg-yellow-400';

    // Control Events
    case 'cancelled':
      return 'bg-gray-500 dark:bg-gray-400';
    case 'status_update':
      return 'bg-sky-500 dark:bg-sky-400';

    // Retry Events
    case 'retry':
      return 'bg-violet-500 dark:bg-violet-400';
    case 'retry_initiated':
      return 'bg-purple-500 dark:bg-purple-400';
    case 'retry_recipient_started':
      return 'bg-fuchsia-500 dark:bg-fuchsia-400';

    default:
      return 'bg-neutral-500 dark:bg-neutral-400';
  }
};

const getRecipientValue = (recipientId: number) => {
  const recipient = props.message.recipients.find(r => r.id === recipientId);
  return recipient?.recipient_value || '';
};

// Methods
const getMessageTypeClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-100 text-blue-500 dark:bg-blue-900/20 dark:text-blue-400';
    case 'email':
      return 'bg-amber-100 text-amber-500 dark:bg-amber-900/20 dark:text-amber-400';
    case 'whatsapp':
      return 'bg-green-100 text-green-500 dark:bg-green-900/20 dark:text-green-400';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-muted text-muted-foreground';
    case 'queued':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'sending':
      return 'bg-blue-500 text-white dark:bg-blue-600';
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'paused':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'cancelled':
      return 'bg-muted text-muted-foreground';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

const getRecipientStatusClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'sent':
      return 'bg-blue-500 text-white dark:bg-blue-600';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'delivered':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'read':
      return 'bg-green-500 text-white dark:bg-green-600';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

const getTypeDotClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-500';
    case 'email':
      return 'bg-amber-500';
    case 'whatsapp':
      return 'bg-green-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusDotClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-400';
    case 'queued':
      return 'bg-blue-500';
    case 'sending':
      return 'bg-blue-500';
    case 'completed':
      return 'bg-green-500';
    case 'paused':
      return 'bg-yellow-500';
    case 'failed':
      return 'bg-red-500';
    case 'cancelled':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
};

const pauseMessage = () => {
  isProcessing.value = true;
  router.patch(`/messages/${props.message.id}/pause`, {}, {
    onFinish: () => {
      isProcessing.value = false;
    }
  });
};

const resumeMessage = () => {
  isProcessing.value = true;
  router.patch(`/messages/${props.message.id}/resume`, {}, {
    onFinish: () => {
      isProcessing.value = false;
    }
  });
};

const cancelMessage = () => {
  isProcessing.value = true;
  router.patch(`/messages/${props.message.id}/cancel`, {}, {
    onFinish: () => {
      isProcessing.value = false;
    }
  });
};

const retryFailedMessages = () => {
  isProcessing.value = true;
  router.patch(`/messages/${props.message.id}/retry`, {}, {
    onFinish: () => {
      isProcessing.value = false;
      // Restart auto-refresh after retry
      if (shouldAutoRefresh.value) {
        stopAutoRefresh();
        startAutoRefresh();
      }
    }
  });
};

const retryRecipient = (recipient: MessageRecipient) => {
  router.patch(`/messages/${props.message.id}/recipients/${recipient.id}/retry`, {}, {
    preserveScroll: true,
    onFinish: () => {
      // Restart auto-refresh after individual retry
      if (shouldAutoRefresh.value) {
        stopAutoRefresh();
        startAutoRefresh();
      }
    }
  });
};

const showDetails = (title: string, message: string, data?: any) => {
  detailsData.value = {
    title,
    message,
    data
  };
  showDetailsDialog.value = true;
};

const showLogDetails = (log: any) => {
  showDetailsDialog.value = true;
  detailsData.value = {
    title: `${log.event ? log.event.toUpperCase() : 'Unknown'} - ${new Date(log.created_at).toLocaleString()}`,
    message: log.description,
    data: {
      event: log.event,
      recipient: log.message_recipient_id ? getRecipientValue(log.message_recipient_id) : null,
      ...log.data
    }
  };
};

const shouldAutoRefresh = computed(() => {
  return ['queued', 'sending', 'paused'].includes(props.message.status);
});

const startAutoRefresh = () => {
  if (refreshTimer.value) return;

  const interval = props.refreshInterval || 5000; // Default to 5 seconds if not specified

  refreshTimer.value = window.setInterval(() => {
    if (shouldAutoRefresh.value && !isRefreshing.value) {
      refreshData();
    }
  }, interval);
};

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    window.clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
};

const refreshData = () => {
  if (isRefreshing.value) return;

  isRefreshing.value = true;
  router.reload({
    onFinish: () => {
      isRefreshing.value = false;

      // If message is complete, stop refreshing
      if (!shouldAutoRefresh.value) {
        stopAutoRefresh();
      }
    },
  });
};

// Get message type from log data
const getLogMessageType = (log: any): string | null => {
  // Check if log has message channels information (new format)
  if (log.data?.message_channels && Array.isArray(log.data.message_channels)) {
    // For multi-channel, show the primary channel or first channel
    return log.data.message_channels[0];
  }

  // Check if log has recipient channel information
  if (log.data?.recipient_channel) {
    return log.data.recipient_channel;
  }

  // For recipient-specific logs, get the channel from the recipient
  if (log.message_recipient_id) {
    const recipient = props.message.recipients.find(r => r.id === log.message_recipient_id);
    if (recipient?.channel) {
      return recipient.channel;
    }
  }

  // Fallback to message primary channel
  if (hasMultipleChannels.value && props.message.channels?.length) {
    return props.message.channels[0];
  }

  // Final fallback for legacy single-channel messages
  if (props.message.type) {
    return props.message.type;
  }

  return null;
};

// Add pagination method
const goToPage = (page: number) => {
  router.get(
    route('messages.show', props.message.id),
    { page },
    {
      preserveState: true,
      preserveScroll: true,
      only: ['logs']
    }
  );
};

// Lifecycle hooks
onMounted(() => {
  if (shouldAutoRefresh.value) {
    startAutoRefresh();
  }
});

onBeforeUnmount(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
