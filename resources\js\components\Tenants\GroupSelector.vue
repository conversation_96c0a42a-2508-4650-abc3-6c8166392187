<template>
  <div class="space-y-4 relative">
    <!-- Search Input -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        v-model="groupSearch"
        type="text"
        placeholder="Search groups by name..."
        class="pl-10"
        autocomplete="new-password"
        @input="searchGroups"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </div>
<!-- Add New Group Button -->
    <div class="flex items-center justify-between">
      <div class="flex items-start gap-2 text-xs text-muted-foreground">
        <HelpCircle class="h-3 w-3 mt-0.5 flex-shrink-0" />
        <div>
          <p>Assign this contact to one or more groups for better organization and targeted messaging.</p>
          <p class="mt-1">Start typing to search for existing groups.</p>
        </div>
      </div>
      <Button
        type="button"
        variant="outline"
        size="sm"
        class="flex-shrink-0"
        @click="openNewGroupDialog"
      >
        <Plus class="h-4 w-4 mr-1" />
        Add New Group
      </Button>
    </div>
    <!-- Search Results Dropdown -->
    <div
      v-if="showDropdown"
      ref="dropdownRef"
      class="absolute top-full left-0 right-0 bg-background border rounded-md shadow-lg overflow-hidden"
      style="z-index: 50"
    >
        <div class="max-h-[300px] overflow-y-auto">
        <div v-if="isLoading" class="h-full flex items-center justify-center p-4">
          <div class="text-center">
            <div class="h-5 w-5 animate-spin mx-auto border-2 border-primary border-t-transparent rounded-full"></div>
            <span class="text-sm text-muted-foreground mt-2 block">Loading...</span>
          </div>
        </div>
        <div v-else>
          <!-- Search Results -->
          <div v-if="searchResults.length > 0">
            <div
              v-for="group in searchResults"
              :key="group.id"
              @click="addGroup(group)"
              class="p-3 hover:bg-muted flex items-center justify-between cursor-pointer group border-b last:border-b-0"
            >
              <div class="flex items-center gap-3">
                <div
                  class="w-4 h-4 rounded-full flex-shrink-0"
                  :style="{ backgroundColor: group.color }"
                ></div>
                <div class="grid gap-0.5 min-w-0">
                  <span class="text-sm font-medium truncate">{{ group.name }}</span>
                  <span class="text-xs text-muted-foreground">
                    {{ group.tenants_count || group.contacts_count || 0 }} {{ (group.tenants_count || group.contacts_count || 0) === 1 ? 'tenant' : 'tenants' }}
                  </span>
                </div>
              </div>
              <Button
                type="button"
                variant="default"
                size="sm"
                class="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
              >
                Add
              </Button>
            </div>
          </div>
          <!-- Empty Search Results -->
          <div
            v-else-if="groupSearch"
            class="h-full flex items-center justify-center p-4"
          >
            <div class="text-center">
              <Users class="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
              <p class="text-sm text-muted-foreground">No groups found</p>
            </div>
          </div>
          <!-- Initial/Empty State -->
          <div v-else class="h-full">
            <div class="p-4 text-center border-b">
              <Users class="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
              <p class="text-sm text-muted-foreground">
                {{ availableGroups.length === 0 ? 'No groups available' : 'Search or select from list' }}
              </p>
            </div>

            <!-- Show initial results if available -->
            <div v-if="availableGroups.length > 0" class="py-2">
              <div
                v-for="group in availableGroups"
                :key="group.id"
                @click="addGroup(group)"
                class="px-4 py-2 hover:bg-muted flex items-center justify-between cursor-pointer group"
              >
                <div class="flex items-center gap-2 min-w-0">
                  <div
                    class="w-4 h-4 rounded-full flex-shrink-0"
                    :style="{ backgroundColor: group.color }"
                  ></div>
                  <span class="text-sm truncate">{{ group.name }}</span>
                </div>
                <span class="text-xs text-muted-foreground">
                  {{ group.tenants_count || group.contacts_count || 0 }} {{ (group.tenants_count || group.contacts_count || 0) === 1 ? 'tenant' : 'tenants' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Selected Groups Display -->
    <div v-if="selectedGroups.length > 0" class="space-y-2">
      <Label class="text-sm font-medium">Selected Groups ({{ selectedGroups.length }})</Label>
      <div class="flex flex-wrap gap-2 p-3 border rounded-lg bg-muted/30">
        <div
          v-for="group in selectedGroups"
          :key="group.id"
          class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border text-white"
          :style="{
            backgroundColor: group.color,
            borderColor: group.color,
            color: 'white'
          }"
        >
          <Users class="h-3 w-3" />
          <span class="text-sm">{{ group.name }}</span>
          <button
            type="button"
            @click="removeGroup(group.id)"
            class="ml-1 hover:bg-black/20 rounded-full p-0.5 transition-colors"
          >
            <X class="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>

    <InputError :message="error" />

    <!-- Create Group Dialog -->
    <Dialog :open="showCreateDialog" @update:open="showCreateDialog = $event">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <Users class="h-5 w-5" />
            Create New Group
          </DialogTitle>
          <DialogDescription>
            Create a new group to organize your contacts
          </DialogDescription>
        </DialogHeader>

        <form @submit.prevent="createGroup" class="space-y-4">
          <div class="space-y-2">
            <Label for="new-group-name">Group Name *</Label>
            <Input
              id="new-group-name"
              v-model="newGroup.name"
              placeholder="Enter group name"
              :class="{ 'border-destructive': newGroup.errors.name }"
              required
            />
            <p v-if="newGroup.errors.name" class="text-sm text-destructive">
              {{ newGroup.errors.name }}
            </p>
          </div>

          <div class="space-y-2">
            <Label for="new-group-description">Description</Label>
            <Textarea
              id="new-group-description"
              v-model="newGroup.description"
              placeholder="Enter group description (optional)"
              rows="2"
            />
          </div>

          <ColorPicker
            v-model="newGroup.color"
            label="Group Color"
            description="Choose a color to help identify this group"
            id="new-group-color"
          />

          <div class="flex items-center justify-between">
            <div class="space-y-1">
              <Label for="new-group-status">Active Group</Label>
              <p class="text-xs text-muted-foreground">
                Active groups can be used for contact organization
              </p>
            </div>
            <Switch
              id="new-group-status"
              v-model="newGroup.status"
            />
          </div>
        </form>

        <DialogFooter class="gap-2">
          <Button variant="outline" @click="cancelCreate">
            Cancel
          </Button>
          <Button @click="createGroup" :disabled="newGroup.processing || !newGroup.name.trim()">
            <span v-if="newGroup.processing" class="flex items-center gap-2">
              <div class="h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
              Creating...
            </span>
            <span v-else class="flex items-center gap-2">
              <Users class="h-4 w-4" />
              Create Group
            </span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Search, Users, X, HelpCircle, Plus } from 'lucide-vue-next';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import InputError from '@/components/InputError.vue';
import ColorPicker from '@/components/ui/ColorPicker.vue';
import { debounce } from 'lodash';

interface Group {
  id: number;
  name: string;
  color: string;
  tenants_count?: number;
  contacts_count?: number; // Legacy field for backward compatibility
}

interface Props {
  modelValue: number[];
  groups: Group[];
  error?: string;
}

interface Emits {
  (e: 'update:modelValue', value: number[]): void;
  (e: 'groupCreated', group: Group): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const dropdownRef = ref<HTMLElement | null>(null);
const showDropdown = ref(false);
const groupSearch = ref('');
const isLoading = ref(false);
const searchResults = ref<Group[]>([]);

// Create group dialog state
const showCreateDialog = ref(false);
const newGroup = ref({
  name: '',
  description: '',
  color: '#6366f1',
  status: true,
  processing: false,
  errors: {} as Record<string, string>
});

// Computed
const selectedGroups = computed(() => {
  return props.modelValue.map(id => {
    const group = props.groups.find(g => g.id === id);
    return group || { id, name: `Group ${id}`, color: '#6366f1', tenants_count: 0, contacts_count: 0 };
  }).filter(Boolean);
});

const availableGroups = computed(() => {
  return props.groups
    .filter(group => !props.modelValue.includes(group.id))
    .slice(0, 10);
});

// Available colors for random selection
const availableColors = [
  '#e6194b', '#3cb44b', '#ffe119', '#4363d8', '#f58231',
  '#911eb4', '#46f0f0', '#f032e6', '#bcf60c', '#fabebe',
  '#008080', '#e6beff', '#9a6324', '#fffac8', '#800000',
  '#aaffc3', '#808000', '#ffd8b1', '#000075', '#808080'
];

// Get random color for new groups
const getRandomColor = () => {
  return availableColors[Math.floor(Math.random() * availableColors.length)];
};

// Methods
const loadInitialGroups = () => {
  if (!groupSearch.value.trim()) {
    searchResults.value = availableGroups.value;
  }
};

const searchGroups = debounce(() => {
  if (!groupSearch.value.trim()) {
    searchResults.value = availableGroups.value;
    return;
  }

  const query = groupSearch.value.toLowerCase();
  searchResults.value = props.groups
    .filter(group =>
      group.name.toLowerCase().includes(query) &&
      !props.modelValue.includes(group.id)
    )
    .slice(0, 10);
}, 300);

const handleFocus = () => {
  showDropdown.value = true;
  loadInitialGroups();
};

const handleBlur = () => {
  // Delay hiding to allow click events to register
  setTimeout(() => {
    showDropdown.value = false;
  }, 150);
};

const addGroup = (group: Group) => {
  if (!props.modelValue.includes(group.id)) {
    emit('update:modelValue', [...props.modelValue, group.id]);
    groupSearch.value = '';
    showDropdown.value = false;
    loadInitialGroups();
  }
};

const removeGroup = (groupId: number) => {
  emit('update:modelValue', props.modelValue.filter(id => id !== groupId));
  if (showDropdown.value) {
    loadInitialGroups();
  }
};

const openNewGroupDialog = () => {
  // Generate random color each time dialog opens
  newGroup.value.color = getRandomColor();
  showCreateDialog.value = true;
};

const createGroup = async () => {
  if (!newGroup.value.name.trim()) {
    newGroup.value.errors.name = 'Group name is required';
    return;
  }

  newGroup.value.processing = true;
  newGroup.value.errors = {};

  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    const response = await fetch('/groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-TOKEN': csrfToken || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
      body: JSON.stringify({
        name: newGroup.value.name,
        description: newGroup.value.description,
        color: newGroup.value.color,
        status: newGroup.value.status,
      }),
    });

    if (response.ok) {
      const data = await response.json();

      if (data.group && data.group.id) {
        // Create new group data
        const newGroupData = {
          id: data.group.id,
          name: data.group.name,
          color: data.group.color,
          tenants_count: 0,
          contacts_count: 0 // Legacy field for backward compatibility
        };

        // Emit event to parent to add the new group
        emit('groupCreated', newGroupData);

        // Auto-select the newly created group
        emit('update:modelValue', [...props.modelValue, data.group.id]);

        // Close dialog and reset form
        showCreateDialog.value = false;
        resetNewGroup();

        // Refresh the dropdown to show the new group
        loadInitialGroups();
      }
    } else {
      const errorData = await response.json();
      if (errorData.errors) {
        newGroup.value.errors = errorData.errors;
      } else {
        newGroup.value.errors.name = 'Failed to create group';
      }
    }
  } catch (error) {
    console.error('Error creating group:', error);
    newGroup.value.errors.name = 'Network error occurred';
  } finally {
    newGroup.value.processing = false;
  }
};

const cancelCreate = () => {
  showCreateDialog.value = false;
  resetNewGroup();
};

const resetNewGroup = () => {
  newGroup.value = {
    name: '',
    description: '',
    color: getRandomColor(), // Set random color for next time
    status: true,
    processing: false,
    errors: {}
  };
};

onMounted(() => {
  loadInitialGroups();
});
</script>
