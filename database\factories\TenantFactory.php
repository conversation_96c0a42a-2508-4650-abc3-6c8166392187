<?php

namespace Database\Factories;

use App\Models\Tenant;
use App\Models\Property;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tenant>
 */
class TenantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Tenant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Randomize contact preferences
        $contact_sms = $this->faker->boolean(70); // 70% chance of being true
        $contact_wa = $this->faker->boolean(50);  // 50% chance of being true
        $contact_email = $this->faker->boolean(80); // 80% chance of being true

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'mobile_phone' => $this->faker->phoneNumber(),
            'contact_sms' => $contact_sms,
            'contact_wa' => $contact_wa,
            'contact_email' => $contact_email,
            'property_id' => Property::factory(), // Will create a property if not assigned
            'unit_number' => $this->faker->bothify('##??'), // Mix of numbers and letters like "12AB"
            'status' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the tenant belongs to a specific property.
     *
     * @param  \App\Models\Property|int  $property
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function forProperty($property)
    {
        return $this->state(function (array $attributes) use ($property) {
            return [
                'property_id' => $property instanceof Property ? $property->id : $property,
            ];
        });
    }

    /**
     * Indicate that the tenant has no property assignment.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withoutProperty()
    {
        return $this->state(function (array $attributes) {
            return [
                'property_id' => null,
            ];
        });
    }
}