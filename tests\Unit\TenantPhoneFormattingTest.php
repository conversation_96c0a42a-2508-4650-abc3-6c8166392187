<?php

namespace Tests\Unit;

use App\Models\Tenant;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

class TenantPhoneFormattingTest extends TestCase
{
    private Tenant $tenant;
    private \ReflectionMethod $formatPhoneNumberMethod;

    protected function setUp(): void
    {
        parent::setUp();

        $this->tenant = new Tenant();

        // Make the private method accessible for testing
        $reflection = new ReflectionClass($this->tenant);
        $this->formatPhoneNumberMethod = $reflection->getMethod('formatPhoneNumber');
        $this->formatPhoneNumberMethod->setAccessible(true);
    }

    /**
     * Test Canadian phone number formatting
     */
    public function test_canadian_phone_number_formats()
    {
        $testCases = [
            '(*************' => '+16473492669',
            '****************' => '+16473492669',
            '************' => '+16473492669',
            '************' => '+16473492669',
            '6473492669' => '+16473492669',
            '16473492669' => '+16473492669',
            '1-************' => '+16473492669',
            '1.************' => '+16473492669',
            '**************' => '+16473492669',
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->formatPhoneNumberMethod->invoke($this->tenant, $input);
            $this->assertEquals($expected, $result, "Failed to format: {$input}");
        }
    }

    /**
     * Test international phone number formatting (for development)
     */
    public function test_international_phone_number_formats()
    {
        $testCases = [
            '+6285758866491' => '+6285758866491',
            '+****************' => '+16473492669',
            '+44 20 7946 0958' => '+442079460958',
            '+33 1 42 86 83 26' => '+33142868326',
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->formatPhoneNumberMethod->invoke($this->tenant, $input);
            $this->assertEquals($expected, $result, "Failed to format international: {$input}");
        }
    }

    /**
     * Test edge cases and error handling
     */
    public function test_edge_cases()
    {
        // Test whitespace handling
        $this->assertEquals(
            '+16473492669',
            $this->formatPhoneNumberMethod->invoke($this->tenant, '  (*************  ')
        );

        // Test with extra characters
        $this->assertEquals(
            '+16473492669',
            $this->formatPhoneNumberMethod->invoke($this->tenant, '************ ext 123')
        );
    }

    /**
     * Test invalid phone numbers
     */
    public function test_invalid_phone_numbers()
    {
        $invalidNumbers = [
            '123',        // Too short
            '12345',      // Too short
            '123456789',  // 9 digits, too short
        ];

        foreach ($invalidNumbers as $invalidNumber) {
            $result = $this->formatPhoneNumberMethod->invoke($this->tenant, $invalidNumber);
            $this->assertNull($result, "Expected null for invalid number: {$invalidNumber}");
        }
    }

    /**
     * Test that the method is used correctly in getPreferredContactMethod
     */
    public function test_get_preferred_contact_method_integration()
    {
        // Create a tenant with formatted phone numbers
        $tenant = new Tenant([
            'mobile_phone' => '(*************',
            'whatsapp_number' => '****************',
            'email' => '<EMAIL>',
            'contact_sms' => true,
            'contact_wa' => true,
            'contact_email' => true,
        ]);

        // Test SMS contact method
        $smsNumber = $tenant->getPreferredContactMethod('sms');
        $this->assertEquals('+16473492669', $smsNumber);

        // Test WhatsApp contact method
        $whatsappNumber = $tenant->getPreferredContactMethod('whatsapp');
        $this->assertEquals('+16473492669', $whatsappNumber);

        // Test email contact method (should not be formatted)
        $email = $tenant->getPreferredContactMethod('email');
        $this->assertEquals('<EMAIL>', $email);
    }
}
